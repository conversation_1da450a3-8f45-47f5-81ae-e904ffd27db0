<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风电集团安全监控系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <div class="logo-container">
                <img src="{{ url_for('static', filename='img/dashboard_company_logo.png') }}" alt="公司Logo" class="logo">
            </div>
            <h1>风电集团安全监控系统</h1>
            <div class="control-panel">
                <button id="stop-monitoring" class="btn danger">停止监控</button>
                <button id="refresh-data" class="btn primary">刷新数据</button>
            </div>
        </header>
        
        <div class="dashboard">
            <div class="left-panel">
                <div class="image-display-container">
                    <h2>图片预览</h2>
                    <div class="image-display-wrapper">
                        <div class="image-display">
                            <img id="current-image" src="" alt="暂无图片">
                            <div class="image-info">
                                <p id="image-job-name">作业名称：</p>
                                <p id="image-job-location">作业地点：</p>
                                <p id="image-camera-name">摄像头：</p>
                                <p id="image-timestamp">时间：</p>
                                <p id="image-has-danger">是否有危险：</p>
                                <p id="image-danger-types">危险类型：</p>
                            </div>
                        </div>
                        <div class="image-controls">
                            <button id="prev-image" class="btn">上一张</button>
                            <button id="next-image" class="btn">下一张</button>
                        </div>
                    </div>
                </div>
                
                <div class="image-list-container">
                    <h2>图片列表</h2>
                    <div class="filter-container">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="start-date">开始：</label>
                                <input type="date" id="start-date">
                            </div>
                            <div class="filter-group">
                                <label for="end-date">结束：</label>
                                <input type="date" id="end-date">
                            </div>
                            <div class="filter-group">
                                <label for="danger-only">
                                    <input type="checkbox" id="danger-only"> 只看危险
                                </label>
                            </div>
                            <div class="filter-group">
                                <label for="danger-filter">类型：</label>
                                <select id="danger-filter">
                                    <option value="all">全部</option>
                                    <option value="黑屏">黑屏</option>
                                    <option value="未佩戴安全帽">未佩戴安全帽</option>
                                    <option value="未佩戴安全带">未佩戴安全带</option>
                                    <option value="吸烟">吸烟</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="time-order">时间：</label>
                                <select id="time-order">
                                    <option value="desc">从晚到早</option>
                                    <option value="asc">从早到晚</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <button id="apply-filter" class="btn">筛选</button>
                            </div>
                        </div>
                    </div>
                    <div class="image-list" id="image-list">
                        <!-- 图片列表将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
            
            <div class="right-panel">
                <h2>作业信息</h2>
                <div class="job-list" id="job-list">
                    <!-- 作业信息将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
        
        <!-- 加载中提示框 -->
        <div id="loading-modal" class="modal">
            <div class="modal-content">
                <div class="spinner"></div>
                <p>正在加载数据，请稍候...</p>
            </div>
        </div>

        <!-- 灯箱特效 -->
        <div id="lightbox" class="lightbox">
            <div class="lightbox-content">
                <span class="lightbox-close">&times;</span>
                <div class="lightbox-main">
                    <div class="lightbox-image-container">
                        <img id="lightbox-image" src="" alt="预览图片">
                        <div class="lightbox-nav">
                            <button id="lightbox-prev" class="lightbox-nav-btn lightbox-prev">‹</button>
                            <button id="lightbox-next" class="lightbox-nav-btn lightbox-next">›</button>
                        </div>
                    </div>
                    <div class="lightbox-info">
                        <h3 id="lightbox-job-name">作业名称</h3>
                        <div class="lightbox-details">
                            <p><strong>地点：</strong><span id="lightbox-job-location">-</span></p>
                            <p><strong>摄像头：</strong><span id="lightbox-camera-name">-</span></p>
                            <p><strong>时间：</strong><span id="lightbox-timestamp">-</span></p>
                            <p><strong>类别：</strong><span id="lightbox-job-category">-</span></p>
                            <p><strong>状态：</strong><span id="lightbox-danger-status">-</span></p>
                            <p><strong>危险类型：</strong><span id="lightbox-danger-types">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html>
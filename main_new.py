"""
风电安全监控系统 - 模块化版本
主程序入口
"""

import threading
import time
from modules import (
    Config, 
    global_state,
    BrowserManager, 
    JobProcessor,
    CameraMonitor, 
    create_app,
    run_app
)


class MonitoringSystem:
    """监控系统主类"""
    
    def __init__(self):
        self.browser_manager = BrowserManager()
        self.job_processor = JobProcessor(self.browser_manager)
        self.camera_monitor = CameraMonitor(self.browser_manager)
        self.web_app = create_app()
        
        # 绑定Web应用的监控启动函数
        self._setup_web_callbacks()
    
    def _setup_web_callbacks(self):
        """设置Web应用回调函数"""
        from flask import request, jsonify

        # 动态添加启动监控路由
        def start_monitoring():
            """启动监控"""
            try:
                data = request.get_json()
                username = data.get('username', '').strip()
                password = data.get('password', '').strip()

                if not username or not password:
                    return jsonify({
                        'success': False,
                        'message': '用户名和密码不能为空'
                    })

                # 在新线程中启动监控
                monitor_thread = threading.Thread(
                    target=self._start_monitoring_task,
                    args=(username, password)
                )
                monitor_thread.daemon = True
                monitor_thread.start()

                return jsonify({
                    'success': True,
                    'message': '监控启动请求已接收，请查看控制台输出'
                })

            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'启动监控失败: {str(e)}'
                })

        # 手动添加路由规则
        self.web_app.add_url_rule('/start_monitoring', 'start_monitoring', start_monitoring, methods=['POST'])
    
    def _start_monitoring_task(self, username: str, password: str):
        """监控任务主流程"""
        try:
            print("=" * 50)
            print("开始执行监控任务")
            print("=" * 50)

            # 重置全局状态
            print("🔄 重置全局状态...")
            try:
                global_state.reset_task_status()
                global_state.monitoring_active = True
                print("✅ 全局状态重置完成")
            except Exception as e:
                print(f"❌ 重置全局状态失败: {str(e)}")
                raise

            # 步骤1: 初始化浏览器
            print("步骤1: 初始化浏览器驱动")
            try:
                if not self.browser_manager.initialize_driver():
                    raise Exception("浏览器初始化失败")
                print("✅ 浏览器驱动初始化成功")
            except Exception as e:
                print(f"❌ 浏览器初始化失败: {str(e)}")
                raise

            # 步骤2: 登录
            print("步骤2: 执行登录")
            try:
                if not self.browser_manager.login(username, password):
                    raise Exception("登录失败")
                print("✅ 登录成功")
            except Exception as e:
                print(f"❌ 登录失败: {str(e)}")
                raise

            # 步骤3: 处理作业，创建标签页
            print("步骤3: 处理作业，创建标签页")
            try:
                job_tabs = self.job_processor.process_jobs()

                if not job_tabs:
                    raise Exception("未找到可监控的作业")

                print(f"✅ 成功创建 {len(job_tabs)} 个作业标签页")
            except Exception as e:
                print(f"❌ 处理作业失败: {str(e)}")
                raise

            # 步骤4: 启动监控
            print("步骤4: 启动摄像头监控")
            try:
                self.camera_monitor.start_monitoring(job_tabs)
                print("✅ 摄像头监控启动成功")
            except Exception as e:
                print(f"❌ 启动监控失败: {str(e)}")
                raise

            # 设置任务状态（与原版本一致，不使用锁）
            global_state.task_status['is_complete'] = False  # 任务正在运行
            global_state.task_status['result'] = f"成功启动 {len(job_tabs)} 个作业的监控"
            print(f"✅ 任务状态已更新: {global_state.task_status['result']}")

            print("=" * 50)
            print("监控任务启动完成")
            print("=" * 50)

            # 保持监控运行（与原版本一致）
            print("🔄 进入监控循环...")
            main_loop_count = 0
            while global_state.monitoring_active:
                main_loop_count += 1
                time.sleep(10)
                print(f"💓 监控运行中... (第{main_loop_count}次检查, monitoring_active={global_state.monitoring_active})")

                # 检查监控线程状态
                active_threads = threading.active_count()
                print(f"📊 当前活跃线程数: {active_threads}")

                # 检查作业信息
                print(f"📋 当前作业数量: {len(global_state.job_info_list)}")
            
        except Exception as e:
            error_msg = f"监控任务执行失败: {str(e)}"
            print(f"❌ {error_msg}")

            # 打印详细的错误信息
            import traceback
            print("详细错误信息:")
            traceback.print_exc()

            # 更新任务状态（与原版本一致，不使用锁）
            global_state.task_status['is_complete'] = True
            global_state.task_status['result'] = error_msg

            # 停止监控
            global_state.stop_monitoring()

        finally:
            # 清理资源
            print("🧹 清理资源...")
            try:
                self.browser_manager.close()
                print("✅ 浏览器已关闭")
            except Exception as e:
                print(f"⚠️ 关闭浏览器时出错: {str(e)}")
    
    def run_web_server(self):
        """运行Web服务器"""
        print("启动Web服务器...")

        # 启动后台监控线程
        monitor_thread = threading.Thread(target=self._background_monitor_listener)
        monitor_thread.daemon = True
        monitor_thread.start()
        print("后台监控监听器已启动")

        run_app(self.web_app)

    def _background_monitor_listener(self):
        """后台监控监听器，监听登录信号"""
        print("🔍 后台监控监听器启动")

        while True:
            try:
                # 检查是否有监控启动请求
                if global_state.start_monitoring_requested and global_state.login_credentials:
                    print("📡 检测到监控启动信号")

                    # 重置信号（避免重复执行）
                    global_state.start_monitoring_requested = False

                    # 获取登录信息
                    credentials = global_state.login_credentials
                    username = credentials['username']
                    password = credentials['password']

                    print(f"🚀 开始启动监控任务: 用户={username}")

                    # 在新线程中启动监控任务，避免阻塞监听器
                    monitor_thread = threading.Thread(
                        target=self._start_monitoring_task_safe,
                        args=(username, password)
                    )
                    monitor_thread.daemon = True
                    monitor_thread.start()

                # 每秒检查一次
                time.sleep(1)

            except Exception as e:
                print(f"❌ 后台监控监听器错误: {str(e)}")
                import traceback
                traceback.print_exc()
                time.sleep(5)  # 出错后等待5秒再重试

    def _start_monitoring_task_safe(self, username: str, password: str):
        """安全的监控任务启动（带完整异常处理）"""
        try:
            self._start_monitoring_task(username, password)
        except Exception as e:
            print(f"❌ 监控任务执行失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def run_console_mode(self):
        """运行控制台模式"""
        print("=" * 50)
        print("风电安全监控系统 - 控制台模式")
        print("=" * 50)
        
        try:
            # 获取登录信息
            username = input("请输入用户名: ").strip()
            password = input("请输入密码: ").strip()
            
            if not username or not password:
                print("❌ 用户名和密码不能为空")
                return
            
            # 启动监控任务
            self._start_monitoring_task(username, password)
            
        except KeyboardInterrupt:
            print("\n用户中断，正在停止监控...")
            global_state.stop_monitoring()
        except Exception as e:
            print(f"❌ 控制台模式运行失败: {str(e)}")
        finally:
            try:
                self.browser_manager.close()
            except:
                pass


def main():
    """主函数"""
    import sys
    import os

    # 设置工作目录为脚本所在目录，确保相对路径正确
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录设置为: {os.getcwd()}")

    # 确保必要的目录存在
    Config.ensure_directories()

    # 创建监控系统
    system = MonitoringSystem()
    
    # 根据命令行参数选择运行模式
    if len(sys.argv) > 1 and sys.argv[1] == '--console':
        # 控制台模式
        system.run_console_mode()
    else:
        # Web模式（默认）
        system.run_web_server()


if __name__ == "__main__":
    main()

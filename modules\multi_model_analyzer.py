"""
多模型并行识别分析器
支持安全帽、安全带、吸烟等危险行为检测
"""

import os
import base64
import json
import asyncio
import concurrent.futures
import tempfile
from typing import Dict, List, Any, Optional
from openai import OpenAI
from PIL import Image


class MultiModelAnalyzer:
    """多模型并行识别分析器"""
    
    def __init__(self):
        """初始化多模型分析器"""
        self.client = OpenAI(
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key="68cfca5c-2edc-49fb-bc8b-7719b60a81c8"
        )
        
        # 三个模型
        self.models = [
            "doubao-1-5-thinking-vision-pro-250428",
            "doubao-1-5-vision-pro-32k-250115", 
            "doubao-seed-1-6-thinking-250715"
        ]
        
        # 提示词模板
        self.prompts = {
            "person_detection": """
你是一个人员检测助手。请分析图像中是否有人员存在。
请返回以下JSON格式：
{
    "has_person": true/false,
    "person_count": 人员数量(数字)
}
""",
            "helmet_detection": """
你是一个安全帽检测助手。请分析图像中的人员是否佩戴安全帽。
请返回以下JSON格式：
{
    "has_helmet_violation": true/false,
    "violation_count": 未佩戴安全帽的人数(数字),
    "total_person": 总人数(数字)
}
""",
            "safety_belt_detection": """
你是一个安全带检测助手。请分析图像中的人员是否佩戴全身安全带（高空作业安全带，不是汽车安全带）。
请返回以下JSON格式：
{
    "has_belt_violation": true/false,
    "violation_count": 未佩戴安全带的人数(数字),
    "total_person": 总人数(数字)
}
""",
            "smoking_detection": """
你是一个吸烟检测助手。请分析图像中是否有人员在吸烟。
请返回以下JSON格式：
{
    "has_smoking": true/false,
    "smoking_count": 正在吸烟的人数(数字),
    "total_person": 总人数(数字)
}
"""
        }
    
    def _convert_to_png_if_needed(self, image_path: str) -> str:
        """
        如果图片不是PNG格式，转换为PNG格式

        Args:
            image_path: 原始图片路径

        Returns:
            str: PNG格式图片路径（可能是临时文件）
        """
        try:
            # 检查文件扩展名
            _, ext = os.path.splitext(image_path.lower())

            if ext == '.png':
                # 已经是PNG格式，直接返回
                return image_path

            # 静默转换，减少日志输出

            # 打开图片并转换为PNG
            with Image.open(image_path) as img:
                # 转换为RGB模式（如果是RGBA或其他模式）
                if img.mode in ('RGBA', 'LA'):
                    # 对于有透明通道的图片，创建白色背景
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'RGBA':
                        background.paste(img, mask=img.split()[-1])  # 使用alpha通道作为mask
                    else:
                        background.paste(img)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')

                # 创建临时PNG文件
                temp_dir = tempfile.gettempdir()
                temp_filename = f"api_temp_{os.path.basename(image_path)}.png"
                temp_path = os.path.join(temp_dir, temp_filename)

                # 保存为PNG
                img.save(temp_path, 'PNG', optimize=True)

                # 转换成功，静默处理
                return temp_path

        except Exception as e:
            print(f"❌ 图片格式转换失败: {str(e)}")
            # 转换失败时返回原始路径
            return image_path

    def _cleanup_temp_file(self, file_path: str, original_path: str):
        """清理临时文件"""
        if file_path != original_path and os.path.exists(file_path):
            try:
                os.remove(file_path)
                # 静默清理临时文件
            except Exception as e:
                print(f"⚠️ 清理临时文件失败: {str(e)}")

    def image_to_base64(self, image_path: str) -> str:
        """将图像转换为base64格式"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"❌ 图像转换base64失败: {str(e)}")
            return ""
    
    def call_single_model(self, model: str, image_b64: str, prompt: str) -> Dict[str, Any]:
        """调用单个模型进行识别"""
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_b64}"
                                },
                            },
                            {
                                "type": "text",
                                "text": prompt
                            },
                        ],
                    }
                ],
                temperature=0.1
            )
            
            content = response.choices[0].message.content
            
            # 尝试解析JSON
            try:
                # 提取JSON部分
                if '{' in content and '}' in content:
                    start = content.find('{')
                    end = content.rfind('}') + 1
                    json_str = content[start:end]
                    result = json.loads(json_str)
                    return {"success": True, "data": result, "model": model}
                else:
                    return {"success": False, "error": "未找到JSON格式", "model": model}
            except json.JSONDecodeError as e:
                return {"success": False, "error": f"JSON解析失败: {str(e)}", "model": model}
                
        except Exception as e:
            return {"success": False, "error": str(e), "model": model}
    
    def parallel_analyze(self, image_path: str, detection_type: str) -> Dict[str, Any]:
        """并行调用三个模型进行分析"""
        if detection_type not in self.prompts:
            return {"success": False, "error": f"不支持的检测类型: {detection_type}"}

        # 转换图像格式（如果需要）
        converted_path = self._convert_to_png_if_needed(image_path)

        try:
            # 转换图像为base64
            image_b64 = self.image_to_base64(converted_path)
            if not image_b64:
                return {"success": False, "error": "图像转换失败"}

            prompt = self.prompts[detection_type]

            # 并行调用三个模型
            with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                futures = [
                    executor.submit(self.call_single_model, model, image_b64, prompt)
                    for model in self.models
                ]

                results = []
                for future in concurrent.futures.as_completed(futures):
                    try:
                        result = future.result(timeout=30)  # 30秒超时
                        results.append(result)
                    except concurrent.futures.TimeoutError:
                        results.append({"success": False, "error": "超时", "model": "unknown"})
                    except Exception as e:
                        results.append({"success": False, "error": str(e), "model": "unknown"})

            return self.vote_results(results, detection_type)

        finally:
            # 清理临时文件
            self._cleanup_temp_file(converted_path, image_path)
    
    def vote_results(self, results: List[Dict[str, Any]], detection_type: str) -> Dict[str, Any]:
        """投票机制决定最终结果"""
        successful_results = [r for r in results if r.get("success", False)]
        
        if len(successful_results) == 0:
            return {
                "success": False,
                "error": "所有模型调用失败",
                "details": results
            }
        
        # 根据检测类型进行投票
        if detection_type == "person_detection":
            return self._vote_person_detection(successful_results)
        elif detection_type == "helmet_detection":
            return self._vote_violation_detection(successful_results, "has_helmet_violation")
        elif detection_type == "safety_belt_detection":
            return self._vote_violation_detection(successful_results, "has_belt_violation")
        elif detection_type == "smoking_detection":
            return self._vote_violation_detection(successful_results, "has_smoking")
        
        return {"success": False, "error": "未知的检测类型"}

    def _parallel_analyze_optimized(self, image_b64: str, prompt: str, detection_type: str) -> Dict[str, Any]:
        """
        优化的并行分析方法，使用预转换的base64图像

        Args:
            image_b64: 预转换的base64图像数据
            prompt: 检测提示词
            detection_type: 检测类型

        Returns:
            Dict: 分析结果
        """
        try:
            # 并行调用三个模型
            with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                futures = [
                    executor.submit(self.call_single_model, model, image_b64, prompt)
                    for model in self.models
                ]

                results = []
                for future in concurrent.futures.as_completed(futures):
                    try:
                        result = future.result(timeout=30)  # 30秒超时
                        results.append(result)
                    except concurrent.futures.TimeoutError:
                        results.append({"success": False, "error": "超时", "model": "unknown"})
                    except Exception as e:
                        results.append({"success": False, "error": str(e), "model": "unknown"})

            return self.vote_results(results, detection_type)

        except Exception as e:
            return {"success": False, "error": f"优化并行分析失败: {str(e)}"}

    def _vote_person_detection(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """人员检测投票"""
        has_person_votes = []
        person_counts = []
        
        for result in results:
            data = result.get("data", {})
            has_person_votes.append(data.get("has_person", False))
            person_counts.append(data.get("person_count", 0))
        
        # 投票决定是否有人
        has_person = sum(has_person_votes) > len(has_person_votes) / 2
        
        # 人数取平均值
        avg_count = sum(person_counts) / len(person_counts) if person_counts else 0
        
        return {
            "success": True,
            "has_person": has_person,
            "person_count": round(avg_count),
            "vote_details": {
                "has_person_votes": has_person_votes,
                "person_counts": person_counts
            }
        }
    
    def _vote_violation_detection(self, results: List[Dict[str, Any]], violation_key: str) -> Dict[str, Any]:
        """违规检测投票"""
        violation_votes = []
        violation_counts = []
        
        for result in results:
            data = result.get("data", {})
            if violation_key == "has_helmet_violation":
                violation_votes.append(data.get("has_helmet_violation", False))
                violation_counts.append(data.get("violation_count", 0))
            elif violation_key == "has_belt_violation":
                violation_votes.append(data.get("has_belt_violation", False))
                violation_counts.append(data.get("violation_count", 0))
            elif violation_key == "has_smoking":
                violation_votes.append(data.get("has_smoking", False))
                violation_counts.append(data.get("smoking_count", 0))
        
        # 投票决定是否有违规
        has_violation = sum(violation_votes) > len(violation_votes) / 2
        
        # 违规人数取平均值
        avg_count = sum(violation_counts) / len(violation_counts) if violation_counts else 0
        
        return {
            "success": True,
            "has_violation": has_violation,
            "violation_count": round(avg_count),
            "vote_details": {
                "violation_votes": violation_votes,
                "violation_counts": violation_counts
            }
        }
    
    def comprehensive_analyze(self, image_path: str, job_category: str = "未知类别") -> Dict[str, Any]:
        """综合分析图像"""
        print(f"🔍 开始综合分析图像: {image_path}")
        print(f"📋 作业类别: {job_category}")
        
        # 1. 首先检测是否有人
        person_result = self.parallel_analyze(image_path, "person_detection")
        if not person_result.get("success", False):
            return {
                "success": False,
                "error": "人员检测失败",
                "details": person_result
            }
        
        # 如果没有人，直接返回
        if not person_result.get("has_person", False):
            return {
                "success": True,
                "has_person": False,
                "has_danger": False,
                "danger_types": [],
                "analysis_details": {
                    "person_detection": person_result
                }
            }
        
        print(f"✅ 检测到有人，继续进行危险行为检测")

        # 2. 根据作业类别确定需要检测的危险行为
        detection_tasks = [
            ("helmet_detection", "安全帽检测"),  # 所有作业都需要检测安全帽
            ("smoking_detection", "吸烟检测")   # 所有作业都不能吸烟
        ]

        # 只有高处作业才需要检测安全带
        is_high_altitude_work = "高处作业" in job_category
        if is_high_altitude_work:
            detection_tasks.append(("safety_belt_detection", "安全带检测"))
            print(f"🏗️ 检测到高处作业，将进行安全带检测")
        else:
            print(f"📋 非高处作业（{job_category}），跳过安全带检测")

        # 3. 优化：预先转换图像格式，避免重复转换
        converted_path = self._convert_to_png_if_needed(image_path)
        image_b64 = self.image_to_base64(converted_path)

        if not image_b64:
            return {
                "success": False,
                "error": "图像转换失败",
                "details": {"image_conversion_error": "无法转换图像为base64"}
            }

        analysis_results = {}
        danger_types = []

        try:
            # 4. 并行执行所有检测任务，使用预转换的图像
            with concurrent.futures.ThreadPoolExecutor(max_workers=len(detection_tasks)) as executor:
                future_to_task = {}

                for task_type, task_name in detection_tasks:
                    if task_type in self.prompts:
                        prompt = self.prompts[task_type]

                        # 为每个检测任务并行调用三个模型
                        future = executor.submit(self._parallel_analyze_optimized, image_b64, prompt, task_type)
                        future_to_task[future] = (task_type, task_name)

                for future in concurrent.futures.as_completed(future_to_task):
                    task_type, task_name = future_to_task[future]
                    try:
                        result = future.result(timeout=45)  # 45秒超时
                        analysis_results[task_type] = result

                        # 检查是否有违规
                        if result.get("success", False) and result.get("has_violation", False):
                            if task_type == "helmet_detection":
                                danger_types.append("未佩戴安全帽")
                            elif task_type == "safety_belt_detection" and is_high_altitude_work:
                                danger_types.append("未佩戴安全带")
                            elif task_type == "smoking_detection":
                                danger_types.append("吸烟")

                        print(f"✅ {task_name}完成")

                    except Exception as e:
                        print(f"❌ {task_name}失败: {str(e)}")
                        analysis_results[task_type] = {
                            "success": False,
                            "error": str(e)
                        }

        finally:
            # 清理临时文件
            self._cleanup_temp_file(converted_path, image_path)
        
        return {
            "success": True,
            "has_person": True,
            "has_danger": len(danger_types) > 0,
            "danger_types": danger_types,
            "analysis_details": {
                "person_detection": person_result,
                **analysis_results
            }
        }

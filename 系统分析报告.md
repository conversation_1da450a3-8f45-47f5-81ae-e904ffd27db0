# 风电安全监控系统 - 详细系统分析报告

## 📋 项目概述

**项目名称**: 风电安全监控系统  
**技术栈**: Python + Flask + Selenium + OpenAI API + OpenCV  
**主要功能**: 自动化监控风电作业现场，实时检测安全违规行为  
**架构模式**: 模块化设计，前后端分离  

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    风电安全监控系统                          │
├─────────────────────────────────────────────────────────────┤
│  前端界面层 (Templates + Static)                            │
│  ├── 登录界面 (index.html)                                  │
│  ├── 监控面板 (dashboard.html)                              │
│  └── 无任务页面 (no_tasks.html)                             │
├─────────────────────────────────────────────────────────────┤
│  Web应用层 (Flask)                                          │
│  ├── 路由管理 (web_app.py)                                  │
│  ├── API接口 (RESTful)                                      │
│  └── 静态资源服务                                           │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层 (Core Modules)                                  │
│  ├── 浏览器管理 (browser_manager.py)                        │
│  ├── 作业处理 (job_processor.py)                            │
│  ├── 摄像头监控 (camera_monitor.py)                         │
│  └── 图像分析 (image_analyzer.py)                           │
├─────────────────────────────────────────────────────────────┤
│  AI分析层 (AI Services)                                     │
│  ├── 多模型分析器 (multi_model_analyzer.py)                 │
│  ├── 黑屏检测                                               │
│  ├── 人员检测                                               │
│  ├── 安全帽检测                                             │
│  ├── 安全带检测                                             │
│  └── 吸烟检测                                               │
├─────────────────────────────────────────────────────────────┤
│  基础设施层 (Infrastructure)                                │
│  ├── 配置管理 (config.py)                                   │
│  ├── 文件工具 (file_utils.py)                               │
│  ├── 全局状态管理                                           │
│  └── 数据存储                                               │
└─────────────────────────────────────────────────────────────┘
```

## 📁 模块详细分析

### 1. 主程序模块 (main_new.py)

#### 1.1 MonitoringSystem 类
**功能**: 系统主控制器，协调各个模块的工作

**核心组件**:
- `browser_manager`: 浏览器管理器实例
- `job_processor`: 作业处理器实例  
- `camera_monitor`: 摄像头监控器实例
- `web_app`: Flask Web应用实例

**主要方法**:
- `_setup_web_callbacks()`: 设置Web应用回调函数
- `_start_monitoring_task()`: 监控任务主流程
- `_background_monitoring_checker()`: 后台监控检查器
- `run()`: 系统启动入口

**工作流程**:
```
启动系统 → 初始化浏览器 → 登录系统 → 获取作业列表 → 创建监控标签页 → 启动摄像头监控 → 持续监控
```

### 2. 配置管理模块 (modules/config.py)

#### 2.1 Config 类
**功能**: 系统配置管理，集中管理所有配置项

**配置分类**:

**目录配置**:
- `SCREENSHOTS_DIR`: 截图保存目录
- `TEMPLATES_DIR`: HTML模板目录
- `STATIC_DIR`: 静态资源目录

**浏览器配置**:
- `EDGE_DRIVER_PATH`: Edge驱动程序路径
- `LOGIN_URL`: 登录页面URL
- `TARGET_URL`: 目标监控页面URL

**截图配置**:
- `DEFAULT_SCREENSHOT_INTERVAL`: 默认截图间隔(30秒)
- `MIN_SCREENSHOT_INTERVAL`: 最小截图间隔(5秒)
- `MAX_SCREENSHOT_INTERVAL`: 最大截图间隔(300秒)

**图像分析配置**:
- `BLACK_THRESHOLD`: 黑屏检测阈值
- `MIN_CONTOUR_AREA`: 最小轮廓面积

#### 2.2 GlobalState 类
**功能**: 全局状态管理，维护系统运行状态和数据

**状态管理**:
- `global_driver`: 全局浏览器驱动实例
- `monitoring_active`: 监控激活状态
- `job_info_list`: 作业信息列表
- `abnormal_images`: 异常图片数据
- `screenshot_interval`: 当前截图间隔

**核心方法**:
- `add_image()`: 添加图片信息
- `set_screenshot_interval()`: 设置截图间隔
- `get_screenshot_interval()`: 获取截图间隔
- `reset_task_status()`: 重置任务状态

### 3. 浏览器管理模块 (modules/browser_manager.py)

#### 3.1 BrowserManager 类
**功能**: 管理Selenium WebDriver，处理浏览器操作

**核心功能**:

**浏览器初始化**:
- Edge浏览器配置
- 无头模式支持
- 窗口大小设置
- 超时配置

**登录管理**:
- 自动填写用户名密码
- 处理登录验证
- 错误处理和重试

**页面导航**:
- URL跳转
- 页面等待
- 元素定位

**主要方法**:
- `initialize_driver()`: 初始化浏览器驱动
- `login()`: 执行登录操作
- `cleanup()`: 清理浏览器资源

### 4. 作业处理模块 (modules/job_processor.py)

#### 4.1 JobProcessor 类
**功能**: 处理作业信息获取和标签页管理

**核心功能**:

**作业信息获取**:
- 解析作业列表页面
- 筛选"作业中"状态的任务
- 提取作业名称、地点、类别信息

**标签页管理**:
- 为每个作业创建独立标签页
- 导航到摄像头监控页面
- 维护标签页状态

**数据处理**:
- 支持二级公司模式
- 处理分页显示(50条/页)
- 异常处理和容错

**主要方法**:
- `process_jobs()`: 处理所有作业
- `_extract_job_info()`: 提取作业信息
- `_create_job_tab()`: 创建作业标签页

### 5. 摄像头监控模块 (modules/camera_monitor.py)

#### 5.1 CameraMonitor 类
**功能**: 核心监控模块，负责摄像头截图和监控调度

**核心功能**:

**摄像头发现**:
- 自动识别页面中的摄像头元素
- 支持多种摄像头布局
- 动态适应页面结构

**截图管理**:
- 定时截图功能
- 动态截图间隔配置
- 多摄像头轮询截图
- 文件命名和路径管理

**监控调度**:
- 多线程并发监控
- 异步AI分析
- 错误恢复机制

**数据流程**:
```
发现摄像头 → 截图保存 → 基础信息记录 → 异步AI分析 → 结果更新 → JSON文件保存
```

**主要方法**:
- `start_monitoring()`: 启动监控
- `_monitor_job()`: 监控单个作业
- `_capture_and_analyze()`: 截图和分析
- `_async_analyze_image()`: 异步图像分析
- `_save_initial_metadata()`: 保存初始元数据
- `_update_metadata_file()`: 更新元数据文件

### 6. 图像分析模块 (modules/image_analyzer.py)

#### 6.1 ImageAnalyzer 类
**功能**: 图像处理和基础分析功能

**核心功能**:

**黑屏检测**:
- 基于像素亮度的黑屏判断
- 可配置的检测阈值
- 支持中文路径的图像读取

**图像预处理**:
- 图像格式转换
- 尺寸调整
- 噪声过滤

**综合分析**:
- 集成多模型分析器
- 结果汇总和格式化
- 异常处理

**主要方法**:
- `comprehensive_analyze_image()`: 综合图像分析
- `_check_black_screen_only()`: 黑屏检测
- `_read_image_with_chinese_path()`: 中文路径图像读取
- `_is_black_screen()`: 黑屏判断算法

### 7. 多模型AI分析模块 (modules/multi_model_analyzer.py)

#### 7.1 MultiModelAnalyzer 类
**功能**: 基于OpenAI API的多模型并行分析

**AI模型配置**:
- `doubao-1-5-thinking-vision-pro-250428`
- `doubao-1-5-vision-pro-32k-250115`
- `doubao-seed-1-6-thinking-250715`

**检测功能**:

**人员检测**:
- 识别图像中的人员数量
- 返回人员位置信息

**安全帽检测**:
- 检测人员是否佩戴安全帽
- 统计违规人数

**安全带检测**:
- 检测高空作业安全带佩戴情况
- 区分汽车安全带和作业安全带

**吸烟检测**:
- 识别吸烟行为
- 统计吸烟人数

**技术特性**:
- 并发分析提高效率
- 结果投票机制确保准确性
- 自动图像格式转换
- 临时文件管理

**主要方法**:
- `analyze_safety_violations()`: 安全违规分析
- `_analyze_with_model()`: 单模型分析
- `_convert_to_png_if_needed()`: 图像格式转换
- `_encode_image()`: 图像编码
- `_parse_json_response()`: 响应解析

### 8. Web应用模块 (modules/web_app.py)

#### 8.1 Flask应用架构
**功能**: 提供Web界面和API服务

**路由管理**:

**页面路由**:
- `/`: 登录主页
- `/dashboard`: 监控面板
- `/no_tasks`: 无任务页面

**API路由**:
- `POST /login`: 处理登录请求
- `GET /api/job_info`: 获取作业信息
- `GET /api/abnormal_images`: 获取图片列表
- `GET /api/task_status`: 获取任务状态
- `GET /screenshots/<path>`: 静态图片服务

**API功能特性**:
- 支持时间范围筛选
- 支持危险类型筛选
- 支持只显示危险图片
- 实时状态更新

**主要方法**:
- `create_app()`: 创建Flask应用
- `register_routes()`: 注册所有路由
- `login_handler()`: 登录处理
- `get_api_abnormal_images()`: 图片API

### 9. 文件工具模块 (modules/file_utils.py)

#### 9.1 FileUtils 类
**功能**: 文件处理和工具函数

**核心功能**:

**文件名处理**:
- 清理非法字符
- 保留中文字符
- 长度限制处理

**目录管理**:
- 自动创建目录
- 路径安全检查
- 权限处理

**图像处理**:
- 安全图像保存
- 中文路径支持
- 错误图像创建

**主要方法**:
- `sanitize_filename()`: 文件名清理
- `create_error_image()`: 创建错误图像
- `ensure_directory()`: 确保目录存在
- `save_image_safe()`: 安全保存图像

## 🎨 前端界面系统

### 1. 登录界面 (templates/index.html)

**功能特性**:
- 用户名密码输入
- 二级公司选项
- 截图间隔配置
- 响应式设计

**JavaScript功能** (static/js/script.js):
- 表单验证
- AJAX登录请求
- 错误提示显示
- 页面跳转控制

### 2. 监控面板 (templates/dashboard.html)

**布局结构**:
```
┌─────────────────────────────────────────────────┐
│  系统状态栏                                      │
├─────────────────────────────────────────────────┤
│  图片预览区域              │  控制按钮           │
│  ├── 图片显示              │  ├── 上一张         │
│  └── 图片信息              │  └── 下一张         │
├─────────────────────────────────────────────────┤
│  筛选控制栏                                      │
│  ├── 时间筛选 ├── 危险筛选 ├── 类型筛选 ├── 按钮  │
├─────────────────────────────────────────────────┤
│  图片列表区域                                    │
│  ├── 图片缩略图                                  │
│  ├── 基本信息                                    │
│  └── 危险标识                                    │
└─────────────────────────────────────────────────┘
```

**JavaScript功能** (static/js/dashboard.js):
- 实时数据更新
- 图片预览控制
- 筛选功能
- 分页显示
- 危险标识

### 3. 样式系统 (static/css/)

**dashboard.css**:
- 监控面板样式
- 图片展示样式
- 筛选控件样式
- 危险标识样式

**style.css**:
- 全局样式
- 登录界面样式
- 响应式布局
- 主题配色

## 🔄 数据流程分析

### 1. 系统启动流程
```
main_new.py启动 → 初始化各模块 → 启动Flask服务 → 等待用户登录
```

### 2. 监控启动流程
```
用户登录 → 验证凭据 → 初始化浏览器 → 获取作业列表 → 创建监控标签页 → 启动摄像头监控
```

### 3. 图像处理流程
```
截图保存 → 基础信息记录 → 异步AI分析 → 结果汇总 → 数据更新 → JSON保存
```

### 4. 前端数据流程
```
页面加载 → API请求 → 数据渲染 → 用户交互 → 筛选更新 → 实时刷新
```

## 🛡️ 安全检测能力

### 1. 检测类型
- **黑屏检测**: 基于像素亮度分析
- **人员检测**: AI视觉识别
- **安全帽检测**: 专业安全帽识别
- **安全带检测**: 高空作业安全带识别
- **吸烟检测**: 吸烟行为识别

### 2. 作业类型适配
- **高处作业**: 重点检测安全带和安全帽
- **动火作业**: 重点检测吸烟行为
- **通用作业**: 全面安全检测

### 3. 检测精度
- **多模型投票**: 3个AI模型并行分析
- **结果验证**: 投票机制确保准确性
- **异常处理**: 完善的错误恢复机制

## ⚙️ 系统配置能力

### 1. 可配置项
- **截图间隔**: 5-300秒可调
- **检测阈值**: 黑屏检测阈值
- **AI模型**: 支持多模型配置
- **存储路径**: 灵活的目录配置

### 2. 运行模式
- **调试模式**: 详细日志输出
- **生产模式**: 精简日志输出
- **无头模式**: 后台运行支持

### 3. 扩展性
- **模块化设计**: 易于添加新功能
- **插件架构**: 支持功能扩展
- **API接口**: 便于集成其他系统

## 📊 性能特性

### 1. 并发处理
- **多线程监控**: 每个作业独立线程
- **异步AI分析**: 不阻塞截图进程
- **并发模型调用**: 提高分析效率

### 2. 资源优化
- **内存管理**: 及时释放图像资源
- **文件管理**: 自动清理临时文件
- **连接池**: 复用HTTP连接

### 3. 容错机制
- **异常恢复**: 自动重试机制
- **状态保持**: 断线重连支持
- **数据完整性**: 确保数据不丢失

## 🔧 部署和维护

### 1. 系统要求
- **操作系统**: Windows 10/11
- **Python版本**: 3.8+
- **浏览器**: Microsoft Edge
- **内存**: 建议8GB+

### 2. 依赖管理
- **核心依赖**: Flask, Selenium, OpenCV, OpenAI
- **驱动程序**: Edge WebDriver
- **AI服务**: 豆包大模型API

### 3. 监控和日志
- **实时日志**: 控制台输出
- **错误追踪**: 详细错误信息
- **性能监控**: 处理时间统计

## 📈 系统优势

### 1. 技术优势
- **模块化架构**: 高内聚低耦合
- **异步处理**: 高性能并发
- **AI驱动**: 智能识别准确
- **Web界面**: 用户友好

### 2. 功能优势
- **全自动化**: 无需人工干预
- **实时监控**: 即时发现问题
- **多维检测**: 全面安全覆盖
- **灵活配置**: 适应不同需求

### 3. 扩展优势
- **易于维护**: 清晰的代码结构
- **便于扩展**: 插件化设计
- **标准接口**: RESTful API
- **文档完善**: 详细的代码注释

## 📝 项目文件结构

```
风电安全监控系统/
├── main_new.py                    # 主程序入口
├── modules/                       # 核心模块目录
│   ├── __init__.py               # 模块初始化
│   ├── config.py                 # 配置管理
│   ├── browser_manager.py        # 浏览器管理
│   ├── job_processor.py          # 作业处理
│   ├── camera_monitor.py         # 摄像头监控
│   ├── image_analyzer.py         # 图像分析
│   ├── multi_model_analyzer.py   # 多模型AI分析
│   ├── web_app.py               # Web应用
│   └── file_utils.py            # 文件工具
├── templates/                    # HTML模板
│   ├── index.html               # 登录页面
│   ├── dashboard.html           # 监控面板
│   └── no_tasks.html           # 无任务页面
├── static/                      # 静态资源
│   ├── css/                    # 样式文件
│   │   ├── style.css          # 全局样式
│   │   └── dashboard.css      # 面板样式
│   ├── js/                     # JavaScript文件
│   │   ├── script.js          # 登录脚本
│   │   └── dashboard.js       # 面板脚本
│   └── img/                    # 图片资源
│       ├── company_logo.png   # 公司Logo
│       ├── wind_farm_bg.jpg   # 背景图片
│       └── image_error.png    # 错误图片
├── screenshots/                 # 截图保存目录
├── edgedriver_win64/           # Edge驱动程序
└── requirements.txt            # 依赖列表
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装Python依赖
pip install -r requirements.txt

# 确保Edge浏览器已安装
# 下载对应版本的Edge WebDriver
```

### 2. 配置设置
```python
# 修改 modules/config.py 中的配置
LOGIN_URL = "你的登录URL"
TARGET_URL = "你的目标URL"
DEFAULT_SCREENSHOT_INTERVAL = 30  # 截图间隔
```

### 3. 启动系统
```bash
# 运行主程序
python main_new.py

# 访问Web界面
http://127.0.0.1:5000
```

### 4. 使用说明
1. 在登录界面输入用户名密码
2. 选择是否为二级公司
3. 设置截图间隔时间
4. 点击登录开始监控
5. 在监控面板查看实时结果

---

**总结**: 这是一个功能完整、架构清晰、技术先进的风电安全监控系统。通过模块化设计实现了高度的可维护性和可扩展性，通过AI技术实现了智能化的安全检测，通过Web界面实现了友好的用户体验。系统具备了生产环境部署的所有必要特性。

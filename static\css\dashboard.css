/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
}

body {
    background-color: #f0f2f5;
    min-height: 100vh;
}

.container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
header {
    background-color: #0056b3;
    color: white;
    padding: 15px 20px;
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.logo-container {
    display: flex;
    align-items: center;
    justify-self: start;
}

.logo {
    width: 200px;
    height: 50px;
    margin-right: 15px;
}

h1 {
    font-size: 20px;
    font-weight: bold;
    text-align: center;
    justify-self: center;
    margin: 0;
}

.control-panel {
    display: flex;
    gap: 10px;
    justify-self: end;
}

/* 按钮样式 */
.btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn:hover {
    opacity: 0.9;
}

.primary {
    background-color: #4caf50;
    color: white;
}

.danger {
    background-color: #f44336;
    color: white;
}

.secondary {
    background-color: #6c757d;
    color: white;
}

/* 仪表板布局 */
.dashboard {
    display: flex;
    height: calc(100vh - 70px);
    padding: 20px;
    gap: 20px;
}

.left-panel {
    width: 66%;  /* 增加左侧面板宽度 */
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.right-panel {
    width: 34%;  /* 减少右侧面板宽度约10% */
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    overflow-y: auto;
}

/* 图片展示区域 */
.image-display-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;  /* 恢复内边距 */
    height: 40%;    /* 提高高度到40% */
    overflow: hidden;
}

.image-display-wrapper {
    display: flex;
    gap: 15px;
    margin-top: 15px;
    height: calc(100% - 80px);
}

.image-display {
    flex: 1;
    display: flex;
}

.image-display img {
    max-width: 60%;
    max-height: 100%;
    object-fit: contain;
    border: 1px solid #ddd;
}

.image-info {
    margin-left: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.image-info p {
    font-size: 14px;  /* 恢复原来的字体大小 */
    line-height: 1.5;
}

.image-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
    justify-content: center;
    min-width: 80px;
}

.image-controls .btn {
    width: 70px;
    padding: 8px 12px;
    font-size: 0.9em;
}

/* 图片列表区域 */
.image-list-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;  /* 恢复内边距 */
    height: 60%;    /* 调整高度到60% */
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.filter-container {
    margin-bottom: 15px;
}

.date-filter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-filter input[type="date"] {
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.image-list {
    overflow-y: auto;
    flex-grow: 1;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 10px;
}

.image-list-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.image-list-item:hover {
    background-color: #f5f5f5;
}

.image-list-item.selected {
    background-color: #e3f2fd;
}

/* 危险图片样式 */
.image-list-item.danger {
    border-left: 4px solid #f44336;
    background-color: #ffebee;
}

.image-list-item.danger:hover {
    background-color: #ffcdd2;
}

.image-list-item.safe {
    border-left: 4px solid #4caf50;
}

.danger-status.danger {
    color: #f44336;
    font-weight: bold;
}

.danger-status.safe {
    color: #4caf50;
}

.danger-types {
    font-size: 0.9em;
    color: #666;
}

/* 筛选容器样式 */
.filter-container {
    margin-bottom: 10px;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 5px;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.filter-group label {
    font-size: 0.9em;
    white-space: nowrap;
}

.filter-group input[type="date"],
.filter-group select {
    padding: 4px 6px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 0.9em;
}

.filter-group .btn {
    padding: 4px 12px;
    font-size: 0.9em;
}

.image-list-item-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    margin-right: 15px;
    border: 1px solid #ddd;
}

.image-list-item-info {
    flex-grow: 1;
}

.image-list-item-info h3 {
    font-size: 14px;
    margin-bottom: 5px;
}

.image-list-item-info p {
    font-size: 12px;
    color: #666;
}

/* 作业信息区域 */
.job-list {
    overflow-y: auto;
    max-height: 100%;
}

.job-item {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.job-item:last-child {
    border-bottom: none;
}

.job-item h3 {
    font-size: 18px;  /* 增大字体大小 */
    margin-bottom: 10px;
    color: #0056b3;
}

.job-item-info {
    margin-bottom: 10px;
}

.job-item-info p {
    margin-bottom: 5px;
    font-size: 16px;  /* 增大字体大小 */
}

.camera-list {
    margin-top: 10px;
    padding-left: 20px;
}

.camera-list li {
    margin-bottom: 5px;
    font-size: 16px;  /* 增大字体大小 */
}

/* 加载动画 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0056b3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 灯箱特效样式 */
.lightbox {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    overflow: auto;
    /* 使用flexbox实现居中 */
    justify-content: center;
    align-items: center;
}

.lightbox-content {
    position: relative;
    width: 95%;
    max-width: 1400px;
    height: 85vh;
    max-height: 800px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    margin: auto;
}

.lightbox-close {
    position: absolute;
    top: 15px;
    right: 25px;
    color: #666;
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
    z-index: 2001;
    transition: color 0.3s ease;
}

.lightbox-close:hover {
    color: #000;
}

.lightbox-main {
    display: flex;
    height: 100%;
    min-height: 600px;
}

.lightbox-image-container {
    flex: 2;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 8px 0 0 8px;
    padding: 20px;
    overflow: hidden; /* 遮罩超出部分 */
}

.lightbox-image-container img {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    cursor: zoom-in;
    transform-origin: center center; /* 设置缩放中心点 */
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    pointer-events: none;
}

.lightbox-nav-btn {
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 24px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    pointer-events: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-nav-btn:hover {
    background-color: rgba(0, 0, 0, 0.7);
}

.lightbox-nav-btn:disabled {
    background-color: rgba(0, 0, 0, 0.2);
    cursor: not-allowed;
}

.lightbox-info {
    flex: 1;
    padding: 30px;
    background-color: white;
    border-radius: 0 8px 8px 0;
    overflow-y: auto;
}

.lightbox-info h3 {
    margin: 0 0 20px 0;
    color: #0056b3;
    font-size: 20px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.lightbox-details p {
    margin: 12px 0;
    font-size: 14px;
    line-height: 1.6;
}

.lightbox-details strong {
    color: #333;
    font-weight: 600;
    display: inline-block;
    min-width: 80px;
}

.lightbox-details span {
    color: #666;
}

/* 图片预览区域的图片添加点击效果 */
.image-display img {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.image-display img:hover {
    transform: scale(1.02);
}

/* 响应式布局 */
@media screen and (max-width: 1200px) {
    .dashboard {
        flex-direction: column;
    }

    .left-panel, .right-panel {
        width: 100%;
    }

    .left-panel {
        height: auto;
    }

    .image-display-container, .image-list-container {
        height: 400px;
    }

    .right-panel {
        height: 400px;
    }
}

/* 灯箱响应式设计 */
@media (max-width: 768px) {
    .lightbox-content {
        width: 98%;
        height: 95vh;
        margin: auto;
    }

    .lightbox-main {
        flex-direction: column;
        height: 100%;
    }

    .lightbox-image-container {
        border-radius: 8px 8px 0 0;
        flex: 1;
    }

    .lightbox-info {
        border-radius: 0 0 8px 8px;
        padding: 20px;
        flex: 0 0 auto;
        max-height: 40%;
        overflow-y: auto;
    }

    .lightbox-nav-btn {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }

    header {
        display: flex;
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .logo-container {
        justify-self: center;
    }

    h1 {
        justify-self: center;
    }

    .control-panel {
        justify-self: center;
    }
}

/* 设置弹窗样式 */
#settings-modal .modal-content {
    width: 400px;
    max-width: 90vw;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close {
    font-size: 24px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: #000;
}

.modal-body {
    margin-bottom: 20px;
    text-align: left;
}

.setting-group {
    margin-bottom: 15px;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.setting-group input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

.help-text {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #666;
}

.modal-footer {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.modal-footer .btn {
    min-width: 80px;
}
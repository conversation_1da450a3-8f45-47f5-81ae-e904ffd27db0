"""
图像分析模块
提供摄像头状态检测、图像处理等功能
"""

import cv2
import numpy as np
import os
from datetime import datetime
from typing import Tuple, Optional, Dict, Any
from .config import Config, global_state
from .file_utils import FileUtils
from .multi_model_analyzer import MultiModelAnalyzer


class ImageAnalyzer:
    """图像分析类"""
    
    def __init__(self, debug_mode: bool = Config.DEBUG_MODE):
        self.debug_mode = debug_mode
        self.black_threshold = Config.BLACK_THRESHOLD
        self.min_contour_area = Config.MIN_CONTOUR_AREA

        # 初始化多模型分析器
        try:
            self.multi_model_analyzer = MultiModelAnalyzer()
            if self.debug_mode:
                print("✅ 多模型分析器已初始化")
        except Exception as e:
            print(f"⚠️ 多模型分析器初始化失败: {str(e)}")
            self.multi_model_analyzer = None

    def _read_image_with_chinese_path(self, image_path: str):
        """
        读取包含中文字符的图像文件路径

        Args:
            image_path: 图像文件路径

        Returns:
            numpy.ndarray: 图像数组，如果读取失败返回None
        """
        try:
            # 使用numpy读取文件，然后用cv2解码
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # 将字节数据转换为numpy数组
            nparr = np.frombuffer(image_data, np.uint8)

            # 使用cv2解码图像
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if self.debug_mode:
                print(f"✅ 成功读取中文路径图像: {image_path}")
                if image is not None:
                    print(f"图像尺寸: {image.shape}")

            return image

        except Exception as e:
            if self.debug_mode:
                print(f"❌ 读取中文路径图像失败: {image_path}, 错误: {str(e)}")
            return None

    def comprehensive_analyze_image(self, image_path: str, job_name: str, camera_name: str, job_category: str = "未知类别", job_location: str = "未知地点") -> Dict[str, Any]:
        """
        综合分析图像，包括黑屏检测和危险行为检测

        Args:
            image_path: 图像文件路径
            job_name: 作业名称
            camera_name: 摄像头名称
            job_category: 作业类别

        Returns:
            Dict: 包含完整分析结果的字典
        """
        if self.debug_mode:
            print(f"🔍 开始综合分析图像: {image_path}")

        # 基础信息
        result = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "image_path": image_path,
            "job_name": job_name,
            "job_location": job_location,
            "camera_name": camera_name,
            "job_category": job_category,
            "is_black_screen": False,
            "has_person": False,
            "has_danger": False,
            "danger_types": [],
            "analysis_details": {}
        }

        try:
            # 1. 首先进行黑屏检测
            is_black = self._check_black_screen_only(image_path)
            result["is_black_screen"] = is_black

            if is_black:
                print("📱 检测到黑屏，跳过危险行为检测")
                result["has_danger"] = True  # 黑屏也算作异常情况
                result["danger_types"] = ["黑屏"]  # 添加黑屏到危险类型
                result["analysis_details"]["black_screen_detection"] = {
                    "is_black": True,
                    "reason": "摄像头黑屏或无信号"
                }

                # 黑屏情况，返回结果（不重复添加到global_state）
                return result

            # 2. 如果不是黑屏且多模型分析器可用，进行危险行为检测
            if self.multi_model_analyzer is not None:
                print("🤖 开始多模型危险行为检测")

                try:
                    ai_result = self.multi_model_analyzer.comprehensive_analyze(image_path, job_category)

                    if ai_result.get("success", False):
                        result["has_person"] = ai_result.get("has_person", False)
                        result["has_danger"] = ai_result.get("has_danger", False)
                        result["danger_types"] = ai_result.get("danger_types", [])
                        result["analysis_details"] = ai_result.get("analysis_details", {})

                        print(f"✅ AI分析完成 - 有人: {result['has_person']}, 有危险: {result['has_danger']}")
                        if result["danger_types"]:
                            print(f"🚨 检测到危险行为: {', '.join(result['danger_types'])}")
                    else:
                        print(f"❌ AI分析失败: {ai_result.get('error', '未知错误')}")
                        result["analysis_details"]["ai_analysis_error"] = ai_result.get("error", "未知错误")

                except Exception as e:
                    print(f"❌ AI分析异常: {str(e)}")
                    result["analysis_details"]["ai_analysis_exception"] = str(e)
            else:
                print("⚠️ 多模型分析器不可用，跳过AI检测")
                result["analysis_details"]["ai_analysis_unavailable"] = "多模型分析器未初始化"

            # 3. 返回分析结果（由camera_monitor负责更新global_state）
            return result

        except Exception as e:
            print(f"❌ 综合分析失败: {str(e)}")
            import traceback
            traceback.print_exc()

            result["analysis_details"]["analysis_error"] = str(e)
            return result

    def _check_black_screen_only(self, image_path: str) -> bool:
        """
        仅检测是否为黑屏

        Args:
            image_path: 图像文件路径

        Returns:
            bool: True表示黑屏，False表示正常
        """
        try:
            # 检查文件是否存在
            if not FileUtils.file_exists(image_path):
                print(f"图像文件不存在: {image_path}")
                return True  # 文件不存在视为黑屏

            # 读取图像
            image = self._read_image_with_chinese_path(image_path)
            if image is None:
                print(f"无法读取图像文件: {image_path}")
                return True  # 无法读取视为黑屏

            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 计算亮度统计
            mean_brightness = np.mean(gray)
            std_brightness = np.std(gray)

            # 检测黑屏
            is_black = self._detect_black_screen(gray, mean_brightness, std_brightness)

            if self.debug_mode:
                print(f"黑屏检测 - 平均亮度: {mean_brightness:.2f}, 标准差: {std_brightness:.2f}, 结果: {'黑屏' if is_black else '正常'}")

            return is_black

        except Exception as e:
            print(f"黑屏检测失败: {str(e)}")
            return True  # 检测失败视为黑屏

    def check_camera_status(self, image_path: str) -> int:
        """
        检测摄像头是否正常开启
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            int: 0表示异常（黑屏），1表示正常
        """
        print(f"开始分析图像: {image_path}")
        if self.debug_mode:
            print("调试模式已启用")

        try:
            # 检查文件是否存在
            if not FileUtils.file_exists(image_path):
                print(f"图像文件不存在: {image_path}")
                return 0

            # 读取图像（支持中文路径）
            image = self._read_image_with_chinese_path(image_path)
            if image is None:
                print(f"无法读取图像文件: {image_path}")
                return 0

            print(f"图像尺寸: {image.shape}")

            # 保存原始图像的副本用于调试
            if self.debug_mode:
                self._save_debug_image(image, image_path, "_debug")

            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 计算图像的基本统计信息
            mean_brightness = np.mean(gray)
            std_brightness = np.std(gray)
            min_brightness = np.min(gray)
            max_brightness = np.max(gray)
            
            print(f"亮度统计 - 平均: {mean_brightness:.2f}, 标准差: {std_brightness:.2f}, "
                  f"最小: {min_brightness}, 最大: {max_brightness}")

            # 检测是否为黑屏
            is_black_screen = self._detect_black_screen(gray, mean_brightness, std_brightness)
            
            if is_black_screen:
                print("检测结果: 摄像头异常（黑屏）")
                status = 0
            else:
                print("检测结果: 摄像头正常")
                status = 1

            # 如果是调试模式或检测到异常，保存分析后的图像
            if self.debug_mode or status == 0:
                self._save_analysis_image(image, gray, image_path, status)

            return status

        except Exception as e:
            print(f"图像分析过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()
            raise Exception(f"图像分析失败: {str(e)}")
    
    def _detect_black_screen(self, gray: np.ndarray, mean_brightness: float, std_brightness: float) -> bool:
        """
        检测是否为黑屏
        
        Args:
            gray: 灰度图像
            mean_brightness: 平均亮度
            std_brightness: 亮度标准差
            
        Returns:
            bool: True表示黑屏，False表示正常
        """
        # 方法1: 检查平均亮度
        if mean_brightness < self.black_threshold:
            print(f"检测到低亮度: {mean_brightness:.2f} < {self.black_threshold}")
            return True
        
        # 方法2: 检查亮度变化（标准差）
        if std_brightness < 5:  # 亮度变化很小，可能是纯色图像
            print(f"检测到低对比度: 标准差 {std_brightness:.2f} < 5")
            return True
        
        # 方法3: 检查边缘数量
        edges = cv2.Canny(gray, 50, 150)
        edge_count = np.sum(edges > 0)
        edge_ratio = edge_count / (gray.shape[0] * gray.shape[1])
        
        print(f"边缘检测: 边缘像素数 {edge_count}, 比例 {edge_ratio:.4f}")
        
        if edge_ratio < 0.01:  # 边缘太少，可能是黑屏或纯色
            print(f"检测到边缘稀少: {edge_ratio:.4f} < 0.01")
            return True
        
        return False
    
    def _save_debug_image(self, image: np.ndarray, original_path: str, suffix: str) -> None:
        """保存调试图像"""
        try:
            image_dir = os.path.dirname(original_path)
            image_basename = os.path.basename(original_path)
            debug_filename = image_basename.replace(".png", f"{suffix}.png")
            debug_path = os.path.join(image_dir, debug_filename)
            
            if FileUtils.save_image_safe(image, debug_path):
                print(f"已保存调试用图像副本: {debug_path}")
            else:
                print(f"保存调试图像失败: {debug_path}")
        except Exception as e:
            print(f"保存调试图像时出错: {str(e)}")
    
    def _save_analysis_image(self, original: np.ndarray, gray: np.ndarray, 
                           original_path: str, status: int) -> None:
        """保存分析后的图像"""
        try:
            # 创建分析结果图像
            debug_img = original.copy()
            
            # 添加分析结果文本
            status_text = "正常" if status == 1 else "异常"
            color = (0, 255, 0) if status == 1 else (0, 0, 255)  # 绿色表示正常，红色表示异常
            
            # 在图像上添加状态文本
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 1
            thickness = 2
            
            # 计算文本位置
            text_size = cv2.getTextSize(status_text, font, font_scale, thickness)[0]
            text_x = debug_img.shape[1] - text_size[0] - 10
            text_y = text_size[1] + 10
            
            # 添加背景矩形
            cv2.rectangle(debug_img, (text_x - 5, text_y - text_size[1] - 5), 
                         (text_x + text_size[0] + 5, text_y + 5), (255, 255, 255), -1)
            
            # 添加文本
            cv2.putText(debug_img, status_text, (text_x, text_y), font, font_scale, color, thickness)
            
            # 保存分析后的图像
            image_dir = os.path.dirname(original_path)
            image_basename = os.path.basename(original_path)
            analyzed_filename = image_basename.replace(".png", "_analyzed.png")
            analyzed_path = os.path.join(image_dir, analyzed_filename)
            
            if FileUtils.save_image_safe(debug_img, analyzed_path):
                print(f"已保存分析后的图像: {analyzed_path}")
            else:
                print(f"保存分析图像失败: {analyzed_path}")
                
        except Exception as e:
            print(f"保存分析图像时出错: {str(e)}")
    
    def analyze_image_quality(self, image_path: str) -> dict:
        """
        分析图像质量
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            dict: 包含图像质量指标的字典
        """
        try:
            # 读取图像（支持中文路径）
            image = self._read_image_with_chinese_path(image_path)
            if image is None:
                return {"error": "无法读取图像"}
            
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 计算各种质量指标
            mean_brightness = float(np.mean(gray))
            std_brightness = float(np.std(gray))
            
            # 计算拉普拉斯方差（清晰度指标）
            laplacian_var = float(cv2.Laplacian(gray, cv2.CV_64F).var())
            
            # 计算边缘密度
            edges = cv2.Canny(gray, 50, 150)
            edge_density = float(np.sum(edges > 0) / (gray.shape[0] * gray.shape[1]))
            
            return {
                "mean_brightness": mean_brightness,
                "std_brightness": std_brightness,
                "laplacian_variance": laplacian_var,
                "edge_density": edge_density,
                "image_size": image.shape,
                "is_clear": laplacian_var > 100,  # 清晰度阈值
                "is_bright_enough": mean_brightness > 30
            }
            
        except Exception as e:
            return {"error": f"分析失败: {str(e)}"}

"""
文件处理工具模块
提供文件名处理、目录管理、错误图像创建等功能
"""

import os
import re
import cv2
import numpy as np
from typing import Optional
from .config import Config


class FileUtils:
    """文件处理工具类"""
    
    @staticmethod
    def sanitize_filename(name: str) -> str:
        """清理文件名，移除不合法字符但保留中文"""
        if not name:
            return "unnamed"

        # 替换Windows文件名不允许的字符
        invalid_chars = r'[<>:"/\\|?*]'
        name = re.sub(invalid_chars, '_', name)
        
        # 移除前后空格和点号
        name = name.strip(' .')
        
        # 限制长度（Windows路径限制）
        if len(name) > 100:
            name = name[:100]
        
        # 如果清理后为空，使用默认名称
        if not name:
            name = "unnamed"

        return name
    
    @staticmethod
    def create_error_image() -> None:
        """创建错误图像占位符"""
        # 使用当前工作目录
        current_dir = os.getcwd()
        error_img_dir = os.path.join(current_dir, Config.STATIC_IMG_DIR)
        os.makedirs(error_img_dir, exist_ok=True)
        error_img_path = os.path.join(error_img_dir, "image_error.png")
        
        # 如果错误图像已存在则跳过
        if os.path.exists(error_img_path):
            return
            
        try:
            # 创建一个红色的错误图像
            img = np.zeros((200, 300, 3), dtype=np.uint8)
            img[:] = (0, 0, 255)  # BGR格式的红色
            
            # 添加文字
            font = cv2.FONT_HERSHEY_SIMPLEX
            text = "Image Error"
            text_size = cv2.getTextSize(text, font, 1, 2)[0]
            text_x = (img.shape[1] - text_size[0]) // 2
            text_y = (img.shape[0] + text_size[1]) // 2
            cv2.putText(img, text, (text_x, text_y), font, 1, (255, 255, 255), 2)
            
            cv2.imwrite(error_img_path, img)
            print(f"已创建错误图像占位符: {error_img_path}")
        except Exception as e:
            print(f"创建错误图像占位符失败: {str(e)}")
    
    @staticmethod
    def ensure_directory(directory: str) -> None:
        """确保目录存在"""
        os.makedirs(directory, exist_ok=True)
    
    @staticmethod
    def get_safe_path(base_dir: str, filename: str) -> str:
        """获取安全的文件路径"""
        safe_filename = FileUtils.sanitize_filename(filename)
        return os.path.join(base_dir, safe_filename)
    
    @staticmethod
    def save_image_safe(image_data: np.ndarray, filepath: str) -> bool:
        """安全保存图像，处理中文路径问题"""
        try:
            # 确保目录存在
            directory = os.path.dirname(filepath)
            FileUtils.ensure_directory(directory)
            
            # 使用cv2.imencode避免中文路径问题
            success, encoded_img = cv2.imencode('.png', image_data)
            if success:
                with open(filepath, 'wb') as f:
                    f.write(encoded_img.tobytes())
                return True
            else:
                print(f"图像编码失败: {filepath}")
                return False
        except Exception as e:
            print(f"保存图像失败: {filepath}, 错误: {str(e)}")
            return False
    
    @staticmethod
    def get_camera_directory(job_name: str, camera_name: str) -> str:
        """获取摄像头专用目录"""
        safe_dir_name = FileUtils.sanitize_filename(f"{job_name}_{camera_name}")
        camera_dir = os.path.join(Config.SCREENSHOTS_DIR, safe_dir_name)
        FileUtils.ensure_directory(camera_dir)
        return camera_dir
    
    @staticmethod
    def generate_filename(timestamp: str, camera_name: str, suffix: str = "") -> str:
        """生成文件名"""
        safe_camera_name = FileUtils.sanitize_filename(camera_name)
        if suffix:
            return f"{timestamp}_{safe_camera_name}_{suffix}.png"
        else:
            return f"{timestamp}_{safe_camera_name}.png"
    
    @staticmethod
    def file_exists(filepath: str) -> bool:
        """检查文件是否存在"""
        return os.path.exists(filepath) and os.path.isfile(filepath)
    
    @staticmethod
    def get_file_size(filepath: str) -> Optional[int]:
        """获取文件大小"""
        try:
            if FileUtils.file_exists(filepath):
                return os.path.getsize(filepath)
            return None
        except Exception:
            return None

"""
Web应用模块
提供Flask Web界面和API
"""

import os
import datetime
from flask import Flask, render_template, request, jsonify, send_from_directory
from typing import Dict, Any

from .config import Config, global_state
from .file_utils import FileUtils


def create_app() -> Flask:
    """创建Flask应用"""
    import os

    # 获取当前工作目录（应该是项目根目录）
    current_dir = os.getcwd()

    # 构建模板和静态文件的完整路径
    template_folder = os.path.join(current_dir, Config.TEMPLATES_DIR)
    static_folder = os.path.join(current_dir, Config.STATIC_DIR)

    print(f"当前工作目录: {current_dir}")
    print(f"模板目录: {template_folder}")
    print(f"静态目录: {static_folder}")
    print(f"模板目录是否存在: {os.path.exists(template_folder)}")

    app = Flask(__name__,
                template_folder=template_folder,
                static_folder=static_folder)

    app.secret_key = Config.FLASK_SECRET_KEY

    # 确保必要的目录存在
    Config.ensure_directories()
    FileUtils.create_error_image()
    
    # 注册路由
    register_routes(app)
    
    return app


def register_routes(app: Flask) -> None:
    """注册所有路由"""
    
    @app.route('/')
    def index():
        """主页"""
        return render_template('index.html')

    @app.route('/login', methods=['POST'])
    def login_handler():
        """处理登录请求"""
        try:
            username = request.form.get('username')
            password = request.form.get('password')
            global_state.is_secondary_company = request.form.get('is_secondary_company') == 'true'
            screenshot_interval = int(request.form.get('screenshot_interval', 30))

            if not username or not password:
                return jsonify({'status': 'error', 'message': '用户名和密码不能为空'})

            # 验证并设置截图间隔
            if not global_state.set_screenshot_interval(screenshot_interval):
                return jsonify({'status': 'error', 'message': '截图间隔设置无效，请输入5-300秒之间的值'})

            # 存储登录信息到全局状态
            global_state.login_credentials = {
                'username': username,
                'password': password,
                'is_secondary_company': global_state.is_secondary_company,
                'screenshot_interval': screenshot_interval
            }

            # 触发监控启动信号
            global_state.start_monitoring_requested = True

            print(f"🔐 收到登录请求: 用户={username}, 二级公司={global_state.is_secondary_company}, 截图间隔={screenshot_interval}秒")
            print("📡 已设置监控启动信号，后台监控器将自动启动")

            return jsonify({'status': 'success', 'message': '登录成功，监控即将启动'})

        except Exception as e:
            return jsonify({'status': 'error', 'message': f'登录失败: {str(e)}'})

    @app.route('/dashboard')
    def dashboard():
        """渲染数据分析主页面"""
        return render_template('dashboard.html')

    @app.route('/no_tasks')
    def no_tasks():
        """显示无任务页面"""
        return render_template('no_tasks.html')

    # start_monitoring路由将在主程序中动态注册
    # 这里保留占位符，避免路由冲突
    
    @app.route('/stop_monitoring', methods=['POST'])
    def stop_monitoring():
        """停止监控"""
        try:
            global_state.stop_monitoring()
            return jsonify({
                'success': True,
                'message': '监控已停止'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'停止监控失败: {str(e)}'
            })
    
    @app.route('/status')
    def get_status():
        """获取监控状态"""
        try:
            with global_state.lock:
                status = {
                    'is_monitoring': global_state.is_monitoring_active(),
                    'task_complete': global_state.task_status['is_complete'],
                    'has_abnormal': global_state.task_status['has_abnormal'],
                    'result': global_state.task_status['result'],
                    'job_count': len(global_state.job_info_list),
                    'abnormal_count': len(global_state.abnormal_images)
                }
            
            return jsonify(status)
            
        except Exception as e:
            return jsonify({
                'error': f'获取状态失败: {str(e)}'
            })
    
    @app.route('/jobs')
    def get_jobs():
        """获取作业信息"""
        try:
            with global_state.lock:
                jobs = global_state.job_info_list.copy()

            return jsonify({
                'success': True,
                'jobs': jobs
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取作业信息失败: {str(e)}'
            })

    @app.route('/api/job_info')
    def get_api_job_info():
        """获取所有作业信息（API格式）"""
        try:
            # 避免锁竞争，直接访问数据
            job_info_copy = global_state.job_info_list.copy() if global_state.job_info_list else []
            print(f"📊 API返回作业信息: {len(job_info_copy)} 个作业")
            return jsonify(job_info_copy)
        except Exception as e:
            print(f"❌ 获取作业信息API失败: {str(e)}")
            return jsonify({'error': f'获取作业信息失败: {str(e)}'})

    @app.route('/api/abnormal_images')
    def get_api_abnormal_images():
        """获取图片列表，支持时间和危险类型筛选"""
        try:
            start_time = request.args.get('start_time')
            end_time = request.args.get('end_time')
            danger_filter = request.args.get('danger_filter')  # 新增：危险类型筛选
            has_danger_only = request.args.get('has_danger_only', 'false').lower() == 'true'  # 新增：只看危险图片
            time_order = request.args.get('time_order', 'desc')  # 新增：时间排序，默认从晚到早

            # 避免锁竞争，直接访问数据
            filtered_images = global_state.abnormal_images.copy() if global_state.abnormal_images else []

            # 时间筛选逻辑
            if start_time:
                try:
                    start_dt = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    filtered_images = [img for img in filtered_images
                                     if datetime.datetime.strptime(img['timestamp'][:10], '%Y-%m-%d') >= start_dt]
                except:
                    pass  # 时间解析失败时跳过筛选

            if end_time:
                try:
                    end_dt = datetime.datetime.strptime(end_time, '%Y-%m-%d')
                    filtered_images = [img for img in filtered_images
                                     if datetime.datetime.strptime(img['timestamp'][:10], '%Y-%m-%d') <= end_dt]
                except:
                    pass  # 时间解析失败时跳过筛选

            # 危险筛选逻辑
            if has_danger_only:
                filtered_images = [img for img in filtered_images
                                 if img.get('has_danger', False) or img.get('is_black_screen', False)]

            if danger_filter and danger_filter != 'all':
                print(f"🔍 应用危险类型筛选: {danger_filter}")
                original_count = len(filtered_images)

                if danger_filter == '黑屏':
                    # 特殊处理黑屏筛选
                    filtered_images = [img for img in filtered_images
                                     if img.get('is_black_screen', False) or danger_filter in img.get('danger_types', [])]
                else:
                    # 其他危险类型筛选
                    filtered_images = [img for img in filtered_images
                                     if danger_filter in img.get('danger_types', [])]

                print(f"📊 筛选前: {original_count} 张，筛选后: {len(filtered_images)} 张")

            # 时间排序逻辑
            try:
                filtered_images.sort(
                    key=lambda x: datetime.datetime.strptime(x['timestamp'], '%Y-%m-%d %H:%M:%S'),
                    reverse=(time_order == 'desc')  # desc=从晚到早(True), asc=从早到晚(False)
                )
            except:
                # 如果时间解析失败，保持原有顺序
                pass

            print(f"📊 API返回图片: {len(filtered_images)} 张，排序: {time_order}")
            return jsonify(filtered_images)

        except Exception as e:
            print(f"❌ 获取图片API失败: {str(e)}")
            return jsonify({'error': f'获取图片失败: {str(e)}'})

    @app.route('/screenshots/<path:filename>')
    def get_screenshot(filename):
        """提供截图文件访问"""
        try:
            # 安全检查
            if '..' in filename or filename.startswith('/'):
                return "Invalid path", 400

            # 使用当前工作目录构建完整路径
            current_dir = os.getcwd()
            file_path = os.path.join(current_dir, Config.SCREENSHOTS_DIR, filename)

            # 检查文件是否存在
            if FileUtils.file_exists(file_path):
                directory = os.path.dirname(file_path)
                basename = os.path.basename(file_path)
                return send_from_directory(directory, basename)
            else:
                # 返回错误图片
                error_img_dir = os.path.join(current_dir, Config.STATIC_IMG_DIR)
                return send_from_directory(error_img_dir, 'image_error.png')

        except Exception as e:
            print(f"提供截图服务失败: {str(e)}")
            current_dir = os.getcwd()
            error_img_dir = os.path.join(current_dir, Config.STATIC_IMG_DIR)
            return send_from_directory(error_img_dir, 'image_error.png')
    
    @app.route('/abnormal_images')
    def get_abnormal_images():
        """获取异常图片列表"""
        try:
            count = request.args.get('count', 10, type=int)
            images = global_state.get_latest_abnormal_images(count)
            
            # 处理图片路径，确保Web可访问
            processed_images = []
            for img in images:
                processed_img = img.copy()
                
                # 检查文件是否存在
                if FileUtils.file_exists(img['filepath_full']):
                    # 转换为相对于static目录的路径
                    rel_path = os.path.relpath(img['filepath_full'], Config.STATIC_DIR)
                    processed_img['web_path'] = f"/static/{rel_path.replace(os.sep, '/')}"
                else:
                    # 使用错误图片占位符
                    processed_img['web_path'] = "/static/img/image_error.png"
                
                processed_images.append(processed_img)
            
            return jsonify({
                'success': True,
                'images': processed_images
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取异常图片失败: {str(e)}'
            })
    
    @app.route('/image/<path:filename>')
    def serve_image(filename):
        """提供图片文件服务"""
        try:
            # 安全检查，防止路径遍历攻击
            if '..' in filename or filename.startswith('/'):
                return "Invalid path", 400
            
            # 构建完整路径
            image_path = os.path.join(Config.SCREENSHOTS_DIR, filename)
            
            # 检查文件是否存在
            if not FileUtils.file_exists(image_path):
                # 返回错误图片
                return send_from_directory(Config.STATIC_IMG_DIR, 'image_error.png')
            
            # 返回图片文件
            directory = os.path.dirname(image_path)
            filename = os.path.basename(image_path)
            return send_from_directory(directory, filename)
            
        except Exception as e:
            print(f"提供图片服务失败: {str(e)}")
            return send_from_directory(Config.STATIC_IMG_DIR, 'image_error.png')
    
    @app.route('/health')
    def health_check():
        """健康检查"""
        return jsonify({
            'status': 'healthy',
            'version': '1.0.0',
            'monitoring_active': global_state.is_monitoring_active()
        })
    
    @app.errorhandler(404)
    def not_found(error):
        """404错误处理"""
        return jsonify({
            'error': 'Not Found',
            'message': '请求的资源不存在'
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """500错误处理"""
        return jsonify({
            'error': 'Internal Server Error',
            'message': '服务器内部错误'
        }), 500


def run_app(app: Flask, host: str = None, port: int = None, debug: bool = None) -> None:
    """运行Flask应用"""
    host = host or Config.FLASK_HOST
    port = port or Config.FLASK_PORT
    debug = debug if debug is not None else Config.FLASK_DEBUG
    
    print(f"启动Web服务器: http://{host}:{port}")
    app.run(host=host, port=port, debug=debug)

document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const loginBtn = document.getElementById('login-btn');
    const errorModal = document.getElementById('error-modal');
    const loadingModal = document.getElementById('loading-modal');
    const resultModal = document.getElementById('result-modal');
    const errorMessage = document.getElementById('error-message');
    const resultText = document.getElementById('result-text');
    const closeButtons = document.querySelectorAll('.close');
    const okBtn = document.getElementById('ok-btn');
    const copyBtn = document.getElementById('copy-btn');
    const closeResultBtn = document.getElementById('close-result-btn');
    
    // 添加登录按钮点击事件
    loginBtn.addEventListener('click', function() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const isSecondaryCompany = document.querySelector('input[name="is_secondary_company"]:checked').value;
        
        if (!username || !password) {
            showErrorModal('用户名和密码不能为空！');
            return;
        }
        
        // 显示加载动画
        showLoadingModal();
        
        // 发送登录请求
        fetch('/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}&is_secondary_company=${encodeURIComponent(isSecondaryCompany)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'error') {
                hideLoadingModal();
                showErrorModal(data.message);
            } else {
                // 开始轮询获取任务状态
                pollTaskStatus();
            }
        })
        .catch(error => {
            hideLoadingModal();
            showErrorModal('请求发生错误：' + error.message);
        });
    });
    
    // 轮询任务状态
    function pollTaskStatus() {
        const interval = setInterval(function() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'complete') {
                        clearInterval(interval);
                        hideLoadingModal();
                        
                        if (data.has_abnormal) {
                            // 如果有异常，显示结果
                            showResultModal(data.result);
                        } else {
                            // 如果没有异常或无任务，跳转到无任务页面
                            window.location.href = '/no_tasks';
                        }
                    }
                    // 如果状态是processing则继续轮询
                })
                .catch(error => {
                    clearInterval(interval);
                    hideLoadingModal();
                    showErrorModal('获取任务状态时出错：' + error.message);
                });
        }, 2000); // 每2秒轮询一次
    }
    
    // 显示错误弹窗
    function showErrorModal(message) {
        errorMessage.textContent = message;
        errorModal.style.display = 'block';
    }
    
    // 显示加载动画
    function showLoadingModal() {
        loadingModal.style.display = 'block';
    }
    
    // 隐藏加载动画
    function hideLoadingModal() {
        loadingModal.style.display = 'none';
    }
    
    // 显示结果弹窗
    function showResultModal(result) {
        resultText.textContent = result;
        resultModal.style.display = 'block';
    }
    
    // 关闭弹窗的点击事件
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = button.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
            }
        });
    });
    
    okBtn.addEventListener('click', function() {
        errorModal.style.display = 'none';
    });
    
    closeResultBtn.addEventListener('click', function() {
        resultModal.style.display = 'none';
    });
    
    // 复制结果内容
    copyBtn.addEventListener('click', function() {
        const text = resultText.textContent;
        navigator.clipboard.writeText(text).then(function() {
            alert('内容已复制到剪贴板');
        }, function() {
            alert('复制失败，请手动复制');
        });
    });
    
    // 点击弹窗外部关闭弹窗
    window.addEventListener('click', function(event) {
        if (event.target === errorModal) {
            errorModal.style.display = 'none';
        }
        if (event.target === resultModal) {
            resultModal.style.display = 'none';
        }
    });
}); 
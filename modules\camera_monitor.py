"""
摄像头监控模块
提供摄像头切换、截图、监控等功能
"""

import os
import time
import threading
import datetime
import json
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from PIL import Image
from typing import List, Dict, Any, Optional

from .config import Config, global_state, lock
from .browser_manager import BrowserManager
from .image_analyzer import ImageAnalyzer
from .file_utils import FileUtils


class CameraMonitor:
    """摄像头监控类"""
    
    def __init__(self, browser_manager: BrowserManager):
        self.browser_manager = browser_manager
        self.image_analyzer = ImageAnalyzer()
        self.monitoring_threads: List[threading.Thread] = []
    
    def start_monitoring(self, job_tabs: List[Dict[str, Any]]) -> None:
        """
        启动监控线程
        
        Args:
            job_tabs: 作业标签页信息列表
        """
        print("开始启动监控线程...")
        
        for tab_info in job_tabs:
            thread = threading.Thread(
                target=self._monitor_job_cameras,
                args=(tab_info,)
            )
            thread.daemon = True
            self.monitoring_threads.append(thread)
            thread.start()
        
        print(f"已启动 {len(self.monitoring_threads)} 个作业监控线程")
    
    def _monitor_job_cameras(self, tab_info: Dict[str, Any]) -> None:
        """监控作业中的所有摄像头（在一个标签页中循环切换）"""
        if not global_state.global_driver:
            print("驱动未初始化，无法监控")
            return
        
        driver = global_state.global_driver
        wait = WebDriverWait(driver, 20)  
        
        # 从tab_info中提取信息
        tab = tab_info["tab"]
        job_name = tab_info["job_name"]
        job_location = tab_info["job_location"]
        camera_names = tab_info["camera_names"]
        job_url = tab_info["url"]

        # 从global_state.job_info_list中获取作业类别
        job_category = "未知类别"
        for job_info in global_state.job_info_list:
            if job_info.get("job_name") == job_name:
                job_category = job_info.get("job_category", "未知类别")
                break

        print(f"开始监控作业: {job_name}, 地点: {job_location}, 类别: {job_category}, 摄像头数量: {len(camera_names)}")
        
        # 计算每个摄像头的截图间隔（使用动态配置）
        total_interval = global_state.get_screenshot_interval()
        camera_interval = total_interval / len(camera_names) if len(camera_names) > 0 else total_interval
        print(f"总截图间隔: {total_interval}秒, 每个摄像头截图间隔: {camera_interval:.1f}秒")
        
        # 主监控循环
        print(f"🔄 作业{job_name} - 进入监控循环，monitoring_active={global_state.monitoring_active}")
        loop_count = 0
        while global_state.monitoring_active:
            try:
                loop_count += 1
                print(f"🔄 作业{job_name} - 开始第{loop_count}轮监控循环")

                for camera_index, camera_name in enumerate(camera_names):
                    print(f"🎯 作业{job_name} - 准备处理摄像头 {camera_index+1}/{len(camera_names)}: '{camera_name}'")

                    if not global_state.monitoring_active:
                        print(f"⚠️ 作业{job_name} - 监控已停止，退出循环")
                        break
                    
                    # 使用锁确保标签页切换的原子性
                    with lock:
                        print(f"\n🎯 作业{job_name} - 切换到摄像头 {camera_index+1}/{len(camera_names)}: '{camera_name}'")

                        # 切换到正确的标签页并复位
                        if not self._switch_to_tab_and_reset(driver, tab, job_url, job_name):
                            continue

                        # 切换摄像头
                        if not self._switch_to_camera(driver, wait, camera_index, camera_name, job_name):
                            continue

                        # 等待摄像头加载
                        print(f"⏱️ 作业{job_name} - 等待5秒让摄像头 '{camera_name}' 完全加载...")
                        time.sleep(5)

                        # 截图并分析
                        screenshot_success = self._capture_and_analyze(
                            driver, wait, job_name, job_location, camera_name, camera_index
                        )

                        if screenshot_success:
                            print(f"✅ 作业{job_name} - 摄像头 '{camera_name}' 截图完成")
                        else:
                            print(f"❌ 作业{job_name} - 摄像头 '{camera_name}' 截图失败")
                    
                    # 等待间隔时间
                    if global_state.monitoring_active and camera_index < len(camera_names) - 1:
                        print(f"⏱️ 等待 {camera_interval:.1f}秒 后切换下一个摄像头...")
                        time.sleep(camera_interval)

                # 完成一轮监控后的等待（使用动态配置）
                if global_state.monitoring_active:
                    total_interval = global_state.get_screenshot_interval()
                    remaining_time = total_interval - (len(camera_names) * camera_interval)
                    if remaining_time > 0:
                        print(f"🔄 作业{job_name} - 完成一轮监控，等待 {remaining_time:.1f}秒 后开始下一轮...")
                        time.sleep(remaining_time)
                    else:
                        print(f"🔄 作业{job_name} - 完成一轮监控，立即开始下一轮...")
                
            except Exception as e:
                print(f"作业{job_name} - 监控循环出错: {str(e)}")
                import traceback
                traceback.print_exc()
                time.sleep(10)  # 出错后等待10秒再重试
    
    def _switch_to_tab_and_reset(self, driver, tab: str, job_url: str, job_name: str) -> bool:
        """切换到标签页并复位滚动位置"""
        try:
            print(f"🔄 作业{job_name} - 开始切换到标签页: {tab}")
            # 切换到标签页
            driver.switch_to.window(tab)
            print(f"✅ 作业{job_name} - 标签页切换完成")
            
            # 验证URL
            current_url = driver.current_url
            if job_url not in current_url:
                print(f"⚠️ URL不匹配，重新加载: 期望包含{job_url}, 实际{current_url}")
                driver.get(job_url)
                time.sleep(3)
            
            # 滚动复位
            print(f"🔄 作业{job_name} - 标签页切换后进行滚动复位")
            scroll_before = self.browser_manager.get_scroll_position()
            print(f"作业{job_name} - 标签页切换前滚动位置: {scroll_before}")
            
            if self.browser_manager.scroll_to_top():
                scroll_after = self.browser_manager.get_scroll_position()
                print(f"作业{job_name} - 标签页切换后滚动位置: {scroll_after}")
                return True
            else:
                print(f"❌ 作业{job_name} - 滚动复位失败")
                return False
                
        except Exception as e:
            print(f"❌ 切换标签页失败: {str(e)}")
            return False
    
    def _switch_to_camera(self, driver, wait: WebDriverWait, camera_index: int, 
                         camera_name: str, job_name: str) -> bool:
        """切换到指定的摄像头"""
        try:
            print(f"🔍 作业{job_name} - 开始切换摄像头流程")

            # 点击摄像头选择下拉框
            print(f"📋 作业{job_name} - 查找摄像头选择下拉框")
            camera_select_input = wait.until(EC.element_to_be_clickable((By.XPATH,
                '//*[@id="app"]/div[1]/div[3]/section/div/main/div[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div/input')))
            print(f"📋 作业{job_name} - 点击摄像头选择下拉框")
            driver.execute_script("arguments[0].click();", camera_select_input)
            time.sleep(2)
            
            # 等待下拉列表出现
            ul_element = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 
                "ul.el-scrollbar__view.el-select-dropdown__list")))
            camera_elements = ul_element.find_elements(By.CSS_SELECTOR, "li.el-select-dropdown__item")
            
            print(f"📋 作业{job_name} - 找到 {len(camera_elements)} 个摄像头选项")
            
            if camera_index < len(camera_elements):
                target_camera = camera_elements[camera_index]
                
                # 获取目标摄像头名称
                try:
                    span_element = target_camera.find_element(By.TAG_NAME, "span")
                    target_name = span_element.text.strip()
                except:
                    target_name = target_camera.text.strip()
                
                print(f"🎯 作业{job_name} - 点击摄像头索引 {camera_index}: '{target_name}'")
                driver.execute_script("arguments[0].click();", target_camera)
                time.sleep(2)
                
                # 验证切换结果
                current_input = driver.find_element(By.XPATH, 
                    '//*[@id="app"]/div[1]/div[3]/section/div/main/div[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div/input')
                current_value = current_input.get_attribute("value") or ""
                
                if target_name in current_value or current_value in target_name:
                    print(f"✅ 作业{job_name} - 摄像头切换成功: '{current_value}'")
                    return True
                else:
                    print(f"❌ 作业{job_name} - 摄像头切换验证失败: 期望包含'{target_name}', 实际'{current_value}'")
                    return False
            else:
                print(f"❌ 作业{job_name} - 摄像头索引 {camera_index} 超出范围")
                return False
                
        except Exception as e:
            print(f"❌ 作业{job_name} - 切换摄像头失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def _capture_and_analyze(self, driver, wait: WebDriverWait, job_name: str,
                           job_location: str, camera_name: str, camera_index: int) -> bool:
        """截图并分析摄像头画面"""
        try:
            # 创建摄像头专用目录
            camera_dir = FileUtils.get_camera_directory(job_name, camera_name)

            # 生成时间戳和文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            meaningful_filename = FileUtils.generate_filename(timestamp, camera_name)
            filepath = os.path.join(camera_dir, meaningful_filename)

            # 查找播放器元素
            target_element = self._find_player_element(driver, wait, job_name)
            if target_element is None:
                return False

            # 滚动定位元素
            if not self._scroll_to_element(driver, target_element, job_name):
                return False

            # 计算截图坐标
            coords = self._calculate_screenshot_coords(driver, target_element, job_name)
            if coords is None:
                return False

            # 截图
            fullpage_filename = FileUtils.generate_filename(timestamp, camera_name, "fullpage")
            fullpage_filepath = os.path.join(camera_dir, fullpage_filename)

            if not self.browser_manager.take_screenshot(fullpage_filepath):
                return False

            # 裁剪图像
            if not self._crop_image(fullpage_filepath, filepath, coords, job_name):
                return False

            # 保存基础图片信息（不进行AI分析，提高截图速度）
            basic_image_info = {
                "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "image_path": filepath,
                "job_name": job_name,
                "job_location": job_location,  # 添加作业地点
                "camera_name": camera_name,
                "job_category": "未知类别",  # 稍后异步获取
                "is_black_screen": False,
                "has_person": False,
                "has_danger": False,
                "danger_types": [],
                "analysis_details": {"status": "pending"}  # 标记为待分析
            }

            # 获取作业类别
            for job_info in global_state.job_info_list:
                if job_info.get("job_name") == job_name:
                    basic_image_info["job_category"] = job_info.get("job_category", "未知类别")
                    break

            # 立即保存基础信息，不等待AI分析
            global_state.add_image(basic_image_info)

            # 异步启动AI分析（不阻塞截图进程）
            import threading
            analysis_thread = threading.Thread(
                target=self._async_analyze_image,
                args=(filepath, job_name, camera_name, basic_image_info["job_category"], job_location),
                daemon=True
            )
            analysis_thread.start()

            # 保存初始元数据（标记为分析中）
            self._save_initial_metadata(camera_dir, timestamp, job_name, job_location,
                                       camera_name, camera_index, meaningful_filename, filepath, basic_image_info)

            # 滚动复位
            self.browser_manager.scroll_to_top()

            return True

        except Exception as e:
            print(f"作业{job_name} - 截图和分析失败: {str(e)}")
            return False

    def _async_analyze_image(self, image_path: str, job_name: str, camera_name: str, job_category: str, job_location: str):
        """
        异步分析图像（独立线程，不阻塞截图）

        Args:
            image_path: 图像文件路径
            job_name: 作业名称
            camera_name: 摄像头名称
            job_category: 作业类别
            job_location: 作业地点
        """
        try:
            print(f"🔍 开始异步分析: {job_name} - {camera_name}")

            # 执行AI分析
            analysis_result = self.image_analyzer.comprehensive_analyze_image(
                image_path, job_name, camera_name, job_category, job_location
            )

            # 更新global_state中的图片信息
            for i, img_info in enumerate(global_state.abnormal_images):
                if (img_info.get("image_path") == image_path and
                    img_info.get("job_name") == job_name and
                    img_info.get("camera_name") == camera_name):

                    # 更新分析结果
                    global_state.abnormal_images[i] = analysis_result
                    print(f"✅ 异步分析完成: {job_name} - {camera_name} - 危险: {analysis_result.get('has_danger', False)}")

                    # 更新JSON文件
                    self._update_metadata_file(analysis_result)
                    break

        except Exception as e:
            print(f"❌ 异步分析失败: {job_name} - {camera_name}: {str(e)}")

    def _find_player_element(self, driver, wait: WebDriverWait, job_name: str):
        """查找播放器元素"""
        player_selectors = [
            (By.XPATH, '//*[@id="player"]'),  # 初代脚本使用的选择器
            (By.CSS_SELECTOR, "canvas.play-window"),
            (By.CSS_SELECTOR, "video.play-window"),
            (By.CSS_SELECTOR, "[id*='player']")
        ]

        print(f"作业{job_name} - 开始查找播放器元素")
        for i, (selector_type, selector) in enumerate(player_selectors):
            try:
                print(f"作业{job_name} - 尝试选择器 {i+1}: {selector}")
                target_element = wait.until(EC.presence_of_element_located((selector_type, selector)))
                print(f"作业{job_name} - ✅ 成功找到播放器元素，使用选择器: {selector}")
                return target_element
            except Exception as e:
                print(f"作业{job_name} - ❌ 选择器 {i+1} 失败: {str(e)}")
                continue

        print(f"作业{job_name} - ❌ 所有选择器都失败，找不到播放器元素")
        return None

    def _scroll_to_element(self, driver, target_element, job_name: str) -> bool:
        """滚动到目标元素"""
        try:
            print(f"作业{job_name} - 开始滚动定位摄像头元素")

            # 获取滚动前位置
            scroll_before = self.browser_manager.get_scroll_position()
            print(f"作业{job_name} - 滚动前位置: {scroll_before}")

            # 执行滚动
            driver.execute_script("arguments[0].scrollIntoView({behavior: 'instant', block: 'center'});", target_element)
            time.sleep(1)

            # 获取滚动后位置
            scroll_after = self.browser_manager.get_scroll_position()
            print(f"作业{job_name} - 滚动后位置: {scroll_after}")

            # 验证滚动是否发生
            scroll_occurred = (scroll_after[0] != scroll_before[0]) or (scroll_after[1] != scroll_before[1])
            if scroll_occurred:
                offset = (scroll_after[0] - scroll_before[0], scroll_after[1] - scroll_before[1])
                print(f"✅ 作业{job_name} - 滚动已发生，偏移量: {offset}")
            else:
                print(f"ℹ️ 作业{job_name} - 元素已在视口内，无需滚动")

            return True

        except Exception as e:
            print(f"作业{job_name} - 滚动定位失败: {str(e)}")
            return False

    def _calculate_screenshot_coords(self, driver, target_element, job_name: str) -> Optional[Dict[str, float]]:
        """计算截图坐标"""
        try:
            # 获取元素位置和大小
            location = target_element.location
            size = target_element.size

            # 获取滚动位置和缩放比例
            scroll_pos = self.browser_manager.get_scroll_position()
            scale = driver.execute_script('return window.devicePixelRatio || 1;')

            print(f"作业{job_name} - 元素位置: {location}, 大小: {size}, 缩放: {scale}")

            # 计算真实坐标
            left = (location['x'] + scroll_pos[0]) * scale
            top = (location['y'] + scroll_pos[1]) * scale
            right = left + (size['width'] * scale)
            bottom = top + (size['height'] * scale)

            coords = {
                'left': left,
                'top': top + 100,  # 向下偏移100像素避免截取界面元素
                'right': right,
                'bottom': bottom
            }

            print(f"作业{job_name} - 计算后坐标: {coords}")
            return coords

        except Exception as e:
            print(f"作业{job_name} - 计算坐标失败: {str(e)}")
            return None

    def _crop_image(self, fullpage_path: str, output_path: str, coords: Dict[str, float], job_name: str) -> bool:
        """裁剪图像"""
        try:
            with Image.open(fullpage_path) as img:
                # 确保坐标在图像范围内
                img_width, img_height = img.size
                left = max(0, min(coords['left'], img_width-1))
                top = max(0, min(coords['top'], img_height-1))
                right = max(left+1, min(coords['right'], img_width))
                bottom = max(top+1, min(coords['bottom'], img_height))

                # 裁剪并保存
                cropped_img = img.crop((int(left), int(top), int(right), int(bottom)))
                cropped_img.save(output_path)
                print(f"作业{job_name} - 裁剪后图像已保存: {output_path}")

                # 删除全页截图（除非是调试模式）
                if not Config.DEBUG_MODE:
                    try:
                        os.remove(fullpage_path)
                    except:
                        pass

                return True

        except Exception as e:
            print(f"作业{job_name} - 裁剪图像失败: {str(e)}")
            return False

    def _save_metadata(self, camera_dir: str, timestamp: str, job_name: str,
                      job_location: str, camera_name: str, camera_index: int,
                      filename: str, filepath: str, status: int) -> None:
        """保存元数据"""
        try:
            metadata_file = os.path.join(camera_dir, f"info_{timestamp}.json")
            metadata = {
                "job_name": job_name,
                "job_location": job_location,
                "camera_name": camera_name,
                "camera_index": camera_index,
                "timestamp": timestamp,
                "filename": filename,
                "path": filepath,
                "status": "异常" if status == 0 else "正常"
            }

            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"保存元数据失败: {str(e)}")

    def _save_initial_metadata(self, camera_dir: str, timestamp: str, job_name: str,
                              job_location: str, camera_name: str, camera_index: int,
                              filename: str, filepath: str, basic_image_info: Dict[str, Any]) -> None:
        """保存初始元数据（分析中状态）"""
        try:
            metadata_file = os.path.join(camera_dir, f"info_{timestamp}.json")
            metadata = {
                "job_name": job_name,
                "job_location": job_location,
                "job_category": basic_image_info.get("job_category", "未知类别"),
                "camera_name": camera_name,
                "camera_index": camera_index,
                "timestamp": basic_image_info.get("timestamp", ""),
                "filename": filename,
                "path": filepath,
                "image_path": filepath,
                "is_black_screen": False,
                "has_person": False,
                "has_danger": False,
                "danger_types": [],
                "analysis_details": {"status": "analyzing"},
                "status": "分析中"
            }

            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"保存初始元数据失败: {str(e)}")

    def _update_metadata_file(self, analysis_result: Dict[str, Any]) -> None:
        """更新JSON元数据文件，包含AI分析结果"""
        try:
            image_path = analysis_result.get("image_path", "")
            if not image_path:
                return

            # 从图片路径推断JSON文件路径
            # 例如: screenshots/1_摄像头/20250803_123456_摄像头.png -> screenshots/1_摄像头/info_20250803_123456.json
            dir_path = os.path.dirname(image_path)
            filename = os.path.basename(image_path)

            # 提取时间戳
            timestamp_match = filename.split('_')[0]  # 假设格式为 20250803_123456_摄像头.png
            if len(filename.split('_')) >= 2:
                timestamp = f"{filename.split('_')[0]}_{filename.split('_')[1]}"
            else:
                timestamp = timestamp_match

            metadata_file = os.path.join(dir_path, f"info_{timestamp}.json")

            # 创建新的元数据格式
            metadata = {
                "job_name": analysis_result.get("job_name", ""),
                "job_location": analysis_result.get("job_location", ""),
                "job_category": analysis_result.get("job_category", ""),
                "camera_name": analysis_result.get("camera_name", ""),
                "timestamp": analysis_result.get("timestamp", ""),
                "image_path": image_path,
                "is_black_screen": analysis_result.get("is_black_screen", False),
                "has_person": analysis_result.get("has_person", False),
                "has_danger": analysis_result.get("has_danger", False),
                "danger_types": analysis_result.get("danger_types", []),
                "analysis_details": analysis_result.get("analysis_details", {}),
                "status": "黑屏" if analysis_result.get("is_black_screen") else ("危险" if analysis_result.get("has_danger") else "正常")
            }

            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

            if analysis_result.get("has_danger") or analysis_result.get("is_black_screen"):
                print(f"📄 已更新JSON文件: {os.path.basename(metadata_file)} - 状态: {metadata['status']}")

        except Exception as e:
            print(f"❌ 更新JSON文件失败: {str(e)}")

    def _record_abnormal_image(self, job_name: str, job_location: str, camera_name: str,
                             timestamp: str, filename: str, filepath: str, status: int) -> None:
        """记录异常图像"""
        try:
            safe_dir_name = FileUtils.sanitize_filename(f"{job_name}_{camera_name}")
            image_info = {
                "filepath": filename,
                "filepath_full": filepath,
                "dir": safe_dir_name,
                "job_name": job_name,
                "job_location": job_location,
                "camera_name": camera_name,
                "timestamp": timestamp,
                "reason": "摄像头黑屏" if status == 0 else "调试模式"
            }

            global_state.add_abnormal_image(image_info)

        except Exception as e:
            print(f"记录异常图像失败: {str(e)}")

    def stop_monitoring(self) -> None:
        """停止监控"""
        global_state.stop_monitoring()
        print("监控已停止")

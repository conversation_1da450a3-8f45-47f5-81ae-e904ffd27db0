# 打包命令
# 有窗口模式
# pyinstaller -F --name=longyuan_power_safety_app --distpath=D:\ --onefile no_weixin.py
# 无窗口模式
# pyinstaller --name=WindPowerSafetyApp --distpath=D:\指定路径 --onefile --noconsole no_weixin.py

import subprocess
import requests
import json
import shutil
from flask import Flask, render_template, request, jsonify, session, send_from_directory
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.edge.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import time
import os
import cv2
import numpy as np
import base64
from PIL import Image
import threading
import io
import datetime
import re

# 用于存储作业信息
job_info_list = []
# 存储是否为二级公司的选项
is_secondary_company = False
# 存储异常图片信息
abnormal_images = []

# 全局变量存储驱动和状态
global_driver = None
monitoring_active = False
monitoring_threads = []

# 全局锁，用于保护共享资源
lock = threading.Lock()

# 使用全局变量存储任务状态和结果
task_status = {
    "is_complete": False,
    "has_abnormal": False,
    "result": ""
}

# 图片保存目录 - 使用相对路径
SCREENSHOTS_DIR = "screenshots"
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)

# 创建错误图像占位符
def create_error_image():
    error_img_dir = os.path.join("static", "img")
    os.makedirs(error_img_dir, exist_ok=True)
    error_img_path = os.path.join(error_img_dir, "image_error.png")
    
    # 如果错误图像已存在则跳过
    if os.path.exists(error_img_path):
        return
        
    try:
        # 创建一个简单的图像占位符
        img = np.ones((300, 400, 3), dtype=np.uint8) * 240  # 浅灰色背景
        
        # 绘制红色叉叉
        cv2.line(img, (50, 50), (350, 250), (0, 0, 255), 5)
        cv2.line(img, (350, 50), (50, 250), (0, 0, 255), 5)
        
        # 添加文本
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(img, "Image Error", (100, 150), font, 1, (0, 0, 0), 2, cv2.LINE_AA)
        
        # 保存图像
        cv2.imwrite(error_img_path, img)
        print(f"已创建错误图像占位符: {error_img_path}")
    except Exception as e:
        print(f"创建错误图像占位符失败: {str(e)}")

# 调试模式 - 设置为True会保存所有图片用于调试
DEBUG_MODE = True

app = Flask(__name__)
app.secret_key = 'wind_power_safety_app_key'
app.config['UPLOAD_FOLDER'] = SCREENSHOTS_DIR

@app.route('/')
def index():
    """渲染主页面"""
    # 重置任务状态
    global task_status
    task_status = {
        "is_complete": False,
        "has_abnormal": False,
        "result": ""
    }
    return render_template('index.html')

@app.route('/login', methods=['POST'])
def login_handler():
    """处理登录请求"""
    global is_secondary_company
    username = request.form.get('username')
    password = request.form.get('password')
    is_secondary_company = request.form.get('is_secondary_company') == 'true'
    
    if not username or not password:
        return jsonify({'status': 'error', 'message': '用户名和密码不能为空！'})
    
    # 将任务放入后台线程执行
    thread = threading.Thread(target=start_task, args=(username, password))
    thread.daemon = True
    thread.start()
    
    return jsonify({'status': 'success', 'message': '登录成功，开始处理任务'})

@app.route('/status')
def get_status():
    """获取任务状态"""
    global task_status, abnormal_images
    
    if not task_status["is_complete"]:
        return jsonify({
            'status': 'processing',
            'abnormal_images': get_latest_abnormal_images(5)  # 返回最新的5个异常图片信息
        })
    else:
        # 返回任务结果
        return jsonify({
            'status': 'complete', 
            'result': task_status["result"], 
            'has_abnormal': task_status["has_abnormal"],
            'abnormal_images': get_latest_abnormal_images(5)  # 返回最新的5个异常图片信息
        })

@app.route('/dashboard')
def dashboard():
    """渲染数据分析主页面"""
    return render_template('dashboard.html')

@app.route('/api/abnormal_images')
def get_abnormal_images():
    """获取异常图片列表，支持时间筛选"""
    global abnormal_images
    start_time = request.args.get('start_time')
    end_time = request.args.get('end_time')
    
    filtered_images = abnormal_images
    
    if start_time:
        start_dt = datetime.datetime.strptime(start_time, '%Y-%m-%d')
        filtered_images = [img for img in filtered_images if datetime.datetime.strptime(img['timestamp'].split('_')[0], '%Y%m%d') >= start_dt]
    
    if end_time:
        end_dt = datetime.datetime.strptime(end_time, '%Y-%m-%d')
        filtered_images = [img for img in filtered_images if datetime.datetime.strptime(img['timestamp'].split('_')[0], '%Y%m%d') <= end_dt]
    
    return jsonify(filtered_images)

@app.route('/api/job_info')
def get_job_info():
    """获取所有作业信息"""
    global job_info_list
    return jsonify(job_info_list)

@app.route('/screenshots/<path:filename>')
def get_screenshot(filename):
    """提供截图文件访问"""
    # 检查是否包含目录
    parts = filename.split('/')
    
    # 如果路径中有多个部分（目录/文件名）
    if len(parts) > 1:
        # 提取目录部分和文件名部分
        directory = '/'.join(parts[:-1])
        file = parts[-1]
        
        # 构建完整路径
        full_path = os.path.join(app.config['UPLOAD_FOLDER'], directory)
        
        # 检查此路径是否存在
        if os.path.exists(os.path.join(full_path, file)):
            return send_from_directory(full_path, file)
    
    # 直接从根目录尝试访问文件
    if os.path.exists(os.path.join(app.config['UPLOAD_FOLDER'], filename)):
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
    
    # 如果找不到文件，使用递归搜索
    for root, dirs, files in os.walk(app.config['UPLOAD_FOLDER']):
        if filename in files:
            # 计算子目录相对于UPLOAD_FOLDER的相对路径
            relative_path = os.path.relpath(root, app.config['UPLOAD_FOLDER'])
            if relative_path == '.':  # 如果文件在UPLOAD_FOLDER目录
                return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
            else:  # 如果文件在子目录
                subdir_path = os.path.join(app.config['UPLOAD_FOLDER'], relative_path)
                return send_from_directory(subdir_path, filename)
    
    # 如果找不到文件，返回错误图片
    try:
        return send_from_directory(os.path.join('static', 'img'), 'image_error.png')
    except:
        # 如果错误图片也找不到，返回404错误
        return "文件不存在", 404

@app.route('/stop_monitoring', methods=['POST'])
def stop_monitoring():
    """停止监控"""
    global monitoring_active
    monitoring_active = False
    return jsonify({'status': 'success', 'message': '监控已停止'})

@app.route('/no_tasks')
def no_tasks():
    """显示无任务页面"""
    return render_template('no_tasks.html')

def sanitize_filename(name):
    """清理文件名，移除不合法字符但保留中文"""
    if not name:
        return "unnamed"

    # 替换Windows文件名不允许的字符
    name = re.sub(r'[\\/*?:"<>|]', "_", name)

    # 替换斜杠为下划线
    name = name.replace('/', '_').replace('\\', '_')

    # 替换连续的下划线为单个下划线
    name = re.sub(r'_+', '_', name)

    # 去除开头和结尾的空格和下划线
    name = name.strip('_ ')

    # 限制长度，避免文件路径过长（考虑中文字符）
    # 中文字符在UTF-8编码中通常占3个字节，所以限制更严格一些
    if len(name.encode('utf-8')) > 150:
        # 逐字符截取，确保不会截断中文字符
        truncated = ""
        for char in name:
            if len((truncated + char).encode('utf-8')) <= 150:
                truncated += char
            else:
                break
        name = truncated

    # 如果名称为空，提供默认名称
    if not name:
        name = "unnamed"

    return name

def get_latest_abnormal_images(count=5):
    """获取最新的count个异常图片信息"""
    global abnormal_images
    with lock:
        sorted_images = sorted(abnormal_images, key=lambda x: x['timestamp'], reverse=True)
        return sorted_images[:count]

def start_task(username, password):
    """后台执行任务"""
    global job_info_list, task_status, global_driver, monitoring_active
    job_info_list = []  # 清空旧结果
    abnormal_images = []  # 清空旧的异常图片
    monitoring_active = True
    
    try:
        # 检查驱动文件是否存在并获取版本信息
        driver_path = os.path.abspath(r'./edgedriver_win64/msedgedriver.exe')
        print(f"使用的驱动路径: {driver_path}")

        # 检查文件是否存在
        if not os.path.exists(driver_path):
            print(f"错误: 驱动文件不存在于 {driver_path}")
            task_status["is_complete"] = True
            return
        
        # 浏览器配置参数
        options = webdriver.EdgeOptions()
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--start-maximized")  # 初始即最大化
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-save-password-bubble')
        options.add_argument('--disable-gpu')  # 禁用GPU加速，减少一些潜在问题
        options.add_argument('--disable-popup-blocking')  # 禁用弹窗阻止
        options.add_argument('--disable-notifications')  # 禁用通知
        
        try:
            service = Service(executable_path=driver_path)
            driver = webdriver.Edge(service=service, options=options)
            driver.maximize_window()  # 双重确保最大化
            global_driver = driver

            # 登录流程
            print("开始登录流程...")
            login_flow(driver, username, password)
            print("登录完成，开始获取作业信息...")

            # 获取所有正在工作中的作业并打开标签页
            job_tabs = handle_target_page(driver)

            if not job_tabs or len(job_tabs) == 0:
                print("没有找到正在工作中的作业")
                task_status["is_complete"] = True
                if global_driver:
                    global_driver.quit()
                    global_driver = None
                return
            
            print(f"成功创建 {len(job_tabs)} 个作业标签页")
            
            # 启动监控线程
            print("开始启动监控线程...")
            start_monitoring_threads(job_tabs)
            print(f"已启动 {len(monitoring_threads)} 个监控线程")
            
            # 设置标志表示任务开始运行
            task_status["is_complete"] = False
            
            # 主线程等待监控停止
            print("主线程开始等待监控任务...")
            try:
                while monitoring_active:
                    time.sleep(5)
                    # 检查所有监控线程状态
                    active_threads = sum(1 for t in monitoring_threads if t.is_alive())
                    print(f"当前活跃监控线程: {active_threads}/{len(monitoring_threads)}")
                    
                    # 如果所有线程都已结束，退出循环
                    if active_threads == 0 and len(monitoring_threads) > 0:
                        print("所有监控线程已结束，停止监控")
                        monitoring_active = False
                        break
            except KeyboardInterrupt:
                print("接收到终止信号，停止监控")
                monitoring_active = False
            
            # 停止所有监控线程
            print("正在等待所有监控线程结束...")
            for thread in monitoring_threads:
                if thread.is_alive():
                    thread.join(2)
            
            # 任务结束，清理资源
            if global_driver:
                print("关闭浏览器驱动...")
                global_driver.quit()
                global_driver = None
            
            # 标记任务完成
            task_status["is_complete"] = True
            task_status["has_abnormal"] = len(abnormal_images) > 0
            print(f"监控任务完成，共检测到 {len(abnormal_images)} 个异常")
            
        except Exception as e:
            print(f"执行任务过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
            if global_driver:
                try:
                    global_driver.quit()
                except:
                    pass
                global_driver = None
            task_status["is_complete"] = True
            
    except Exception as e:
        print(f"启动任务时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        task_status["is_complete"] = True

def login_flow(driver, username, password):
    """处理登录流程"""
    login_url = "https://id.ceic.com/auth/realms/sh4a/protocol/openid-connect/auth?client_id=JTajhxt&redirect_uri=http%3A%2F%2Fsafety.ceic.com&response_type=code&scope=openid&state=1918223715797377024"

    # 访问登录页
    driver.get(login_url)
    wait = WebDriverWait(driver, 20)
    
    # 处理可能出现的任何警告
    try:
        alert = driver.switch_to.alert
        print(f"检测到警告: {alert.text}")
        alert.accept()  # 点击确定按钮
        time.sleep(1)
    except Exception as e:
        # 没有警告弹窗，继续正常流程
        pass
    
    # 输入凭据
    enter_credentials(wait, username, password)
    
    # 处理登录后可能出现的警告
    try:
        alert = WebDriverWait(driver, 3).until(EC.alert_is_present())
        print(f"登录后检测到警告: {alert.text}")
        alert.accept()
    except Exception:
        pass

    # 等待公司Logo出现（标志登录成功或页面加载完成）
    try:
        wait.until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="app"]/div[1]/div[2]/div[1]/img'))
        )
        print("登录成功")
    except Exception as e:
        print(f"等待Logo出现时出错: {str(e)}")
        # 检查是否有警告
        try:
            alert = driver.switch_to.alert
            print(f"发现警告: {alert.text}")
            alert.accept()
        except:
            pass

def enter_credentials(wait, username, password):
    """输入登录信息"""
    try:
        username_field = wait.until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "#username"))
        )
        username_field.clear()
        username_field.send_keys(username)

        password_field = wait.until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "#password"))
        )
        password_field.clear()
        password_field.send_keys(password)

        # 点击登录按钮
        wait.until(
            EC.element_to_be_clickable((By.XPATH, '//*[@id="kc-submit"]'))
        ).click()
    except Exception as e:
        # 检查是否有意外警告弹出
        try:
            alert = wait._driver.switch_to.alert
            print(f"处理意外警告: {alert.text}")
            alert.accept()  # 点击确定
            # 刷新页面重试
            wait._driver.refresh()
            time.sleep(2)
            # 递归调用自身重试
            enter_credentials(wait, username, password)
            return
        except Exception:
            # 如果不是警告问题，重新抛出原始异常
            raise e

    # 判断是否存在特定元素并点击
    try:
        wait.until(
            EC.element_to_be_clickable((By.XPATH, '//*[@id="info_flash"]/div/div[3]/a[2]')),
        ).click()
    except (TimeoutException, NoSuchElementException):
        # 若元素不存在或超时未出现，跳过该步骤
        pass

def handle_target_page(driver):
    """处理目标页面，找出所有正在工作中的作业"""
    global is_secondary_company, job_info_list
    target_url = "http://safety.ceic.com/machinery/20/40"
    job_tabs = []
    
    wait = WebDriverWait(driver, 20)
    
    # 访问目标页面
    driver.get(target_url)
    
    # 等待页面加载完成
    wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

    try:
        # 点击分页设置
        pagination_dropdown = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(@class,'pagination')]//input"))
        )
        driver.execute_script("arguments[0].click();", pagination_dropdown)

        # 选择50条/页
        select_option = wait.until(
            EC.visibility_of_element_located((By.XPATH, "//li[contains(.,'50条/页')]"))
        )
        driver.execute_script("arguments[0].click();", select_option)

        # 等待数据加载
        wait.until(
            EC.invisibility_of_element_located((By.CSS_SELECTOR, ".el-loading-mask"))
        )

        # 定位tbody并获取所有行
        tbody = wait.until(EC.presence_of_element_located((By.XPATH, "//tbody")))

        # 获取所有"作业中"的行索引
        job_in_progress_indexes = []
        rows = tbody.find_elements(By.XPATH, "./tr")
        for index, row in enumerate(rows, start=1):  # 索引从1开始
            try:
                # 获取最后一个td的状态
                last_td = row.find_element(By.XPATH, "./td[last()]")
                status = last_td.text.strip()

                if status == "作业中":
                    job_in_progress_indexes.append(index)

            except NoSuchElementException:
                continue  # 跳过异常行

        # 添加无任务时的提示
        if not job_in_progress_indexes:
            print("当前没有正在工作中的任务")
            return []

        # 根据二级公司选项确定要使用的td索引
        td_index = 4 if is_secondary_company else 3

        # 为每个作业创建一个标签页
        main_window = driver.current_window_handle
        
        for row_index in job_in_progress_indexes:
            try:
                print(f"处理第{row_index}行作业")
                
                # 重新获取tbody和所有行
                tbody = wait.until(EC.presence_of_element_located((By.XPATH, "//tbody")))
                rows = tbody.find_elements(By.XPATH, "./tr")
                row = rows[row_index - 1]

                # 获取作业名称和地点信息用于后续使用
                try:
                    job_name_td = row.find_element(By.XPATH, f"./td[2]")
                    job_name = job_name_td.text.strip()
                    job_location_td = row.find_element(By.XPATH, f"./td[3]")
                    job_location = job_location_td.text.strip()
                except Exception as e:
                    print(f"获取作业信息失败: {e}")
                    job_name = f"作业{row_index}"
                    job_location = "未知地点"

                # 根据二级公司选项获取对应列
                target_td = row.find_element(By.XPATH, f"./td[{td_index}]")
                link = target_td.find_element(By.XPATH, ".//a")

                # 点击链接打开作业详情（移除不必要的滚动操作）
                try:
                    driver.execute_script("arguments[0].click();", link)
                    wait.until(EC.presence_of_element_located((By.XPATH, '//div[contains(@class,"app-container")]')))
                except Exception as e:
                    print(f"打开作业详情失败: {e}")
                    continue

                # 等待作业详情页面加载
                time.sleep(3)

                # 获取摄像头列表（用于创建作业标签页）
                try:
                    # 点击打开摄像头列表
                    camera_select_input = wait.until(EC.element_to_be_clickable((By.XPATH,
                        '//*[@id="app"]/div[1]/div[3]/section/div/main/div[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div/input')))
                    driver.execute_script("arguments[0].click();", camera_select_input)
                    time.sleep(1)  # 等待下拉列表展开

                    # 等待摄像头列表出现，使用更精确的选择器
                    ul_element = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR,
                        "ul.el-scrollbar__view.el-select-dropdown__list")))
                    camera_elements = ul_element.find_elements(By.CSS_SELECTOR, "li.el-select-dropdown__item")
                    camera_names = []

                    print(f"找到 {len(camera_elements)} 个摄像头选项")

                    # 确保正确获取摄像头名称
                    for i, li in enumerate(camera_elements):
                        try:
                            # 尝试多种方式获取摄像头名称
                            name = None

                            # 方法1: 直接查找span元素
                            try:
                                span_element = li.find_element(By.TAG_NAME, "span")
                                name = span_element.text.strip()
                            except:
                                pass

                            # 方法2: 如果span方法失败，尝试获取li的文本内容
                            if not name:
                                name = li.text.strip()

                            # 方法3: 如果还是没有，使用innerHTML
                            if not name:
                                name = li.get_attribute("innerHTML")
                                if name:
                                    # 简单清理HTML标签
                                    import re
                                    name = re.sub(r'<[^>]+>', '', name).strip()

                            if name and name != "":
                                print(f"发现摄像头 {i+1}: '{name}'")
                                camera_names.append(name)
                            else:
                                fallback_name = f"摄像头_{i+1}"
                                print(f"摄像头 {i+1} 名称为空，使用备用名称: '{fallback_name}'")
                                camera_names.append(fallback_name)

                        except Exception as e:
                            fallback_name = f"未命名摄像头_{len(camera_names)+1}"
                            print(f"获取摄像头 {i+1} 名称失败: {str(e)}，使用备用名称: '{fallback_name}'")
                            camera_names.append(fallback_name)
                    
                    # 记录作业信息
                    job_info = {
                        "job_name": job_name,
                        "job_location": job_location,
                        "cameras": camera_names
                    }
                    job_info_list.append(job_info)
                    
                    # 获取当前URL（作业页面URL）
                    job_page_url = driver.current_url
                    
                    # 获取当前URL（作业页面URL）
                    job_page_url = driver.current_url
                    print(f"作业页面URL: {job_page_url}")

                    # 关闭摄像头选择下拉框（如果还开着）
                    try:
                        driver.execute_script("document.body.click();")
                        time.sleep(1)
                    except:
                        pass

                    # 为当前作业创建一个标签页，在其中循环切换摄像头
                    print(f"\n=== 为作业 {job_name} 创建标签页（包含 {len(camera_names)} 个摄像头）===")

                    try:
                        # 步骤1: 确保当前在主窗口
                        print("1. 切换到主窗口")
                        driver.switch_to.window(main_window)
                        time.sleep(2)  # 等待2秒

                        # 步骤2: 打开新标签页
                        print("2. 创建作业标签页")
                        driver.execute_script("window.open('about:blank');")
                        time.sleep(2)  # 等待2秒

                        # 步骤3: 获取新标签页句柄
                        job_tab = driver.window_handles[-1]
                        print(f"3. 作业标签页句柄: {job_tab}")

                        # 步骤4: 切换到新标签页
                        print("4. 切换到作业标签页")
                        driver.switch_to.window(job_tab)
                        time.sleep(2)  # 等待2秒

                        # 步骤5: 导航到作业页面
                        print("5. 导航到作业页面")
                        driver.get(job_page_url)
                        time.sleep(3)  # 等待页面加载

                        # 步骤6: 等待页面完全加载
                        print("6. 等待页面完全加载")
                        wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="app"]')))
                        time.sleep(2)  # 等待2秒

                        # 步骤7: 验证摄像头列表并保存作业信息
                        print("7. 验证摄像头列表")

                        # 将作业标签页信息保存到列表中
                        job_tab_info = {
                            "tab": job_tab,
                            "job_name": job_name,
                            "job_location": job_location,
                            "camera_names": camera_names,  # 保存所有摄像头名称
                            "url": job_page_url
                        }

                        job_tabs.append(job_tab_info)
                        print(f"✅ 作业标签页创建完成 - 作业: {job_name}, 摄像头数量: {len(camera_names)}")

                    except Exception as e:
                        print(f"创建作业标签页失败: {str(e)}")
                        # 如果创建标签页失败，尝试关闭可能创建的标签页
                        try:
                            if len(driver.window_handles) > 1:  # 有多个窗口时才关闭
                                driver.close()
                        except:
                            pass

                    # 步骤8: 切回主窗口
                    print("8. 切回主窗口")
                    driver.switch_to.window(main_window)
                    time.sleep(2)  # 等待2秒
                    
                    # 返回主页面
                    driver.get(target_url)
                    
                    # 等待页面加载
                    wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                    
                    # 重新设置分页
                    pagination_dropdown = wait.until(
                        EC.element_to_be_clickable((By.XPATH, "//div[contains(@class,'pagination')]//input"))
                    )
                    driver.execute_script("arguments[0].click();", pagination_dropdown)
                    
                    select_option = wait.until(
                        EC.visibility_of_element_located((By.XPATH, '/html/body/div[2]/div[1]/div[1]/ul/li[4]'))
                    )
                    driver.execute_script("arguments[0].click();", select_option)
                    
                    # 等待数据加载
                    wait.until(
                        EC.invisibility_of_element_located((By.CSS_SELECTOR, ".el-loading-mask"))
                    )
                    
                except Exception as e:
                    print(f"处理作业摄像头时出错: {str(e)}")
                    continue

            except Exception as e:
                print(f"处理第{row_index}行时出错: {str(e)}")
                continue

        return job_tabs

    except Exception as e:
        print(f"处理目标页面时出错: {str(e)}")
        return []

def monitor_job_cameras(tab_info):
    """监控作业中的所有摄像头（在一个标签页中循环切换）"""
    global global_driver, monitoring_active, abnormal_images, lock

    if not global_driver:
        print("驱动未初始化，无法监控")
        return

    # 从tab_info中提取信息
    tab = tab_info["tab"]
    job_name = tab_info["job_name"]
    job_location = tab_info["job_location"]
    camera_names = tab_info["camera_names"]
    job_url = tab_info["url"]

    print(f"开始监控作业: {job_name}, 地点: {job_location}, 摄像头数量: {len(camera_names)}")

    # 计算每个摄像头的截图间隔（确保每30秒一个截图）
    # 如果有2个摄像头，每个摄像头15秒；如果有3个摄像头，每个摄像头10秒
    camera_interval = 30 / len(camera_names) if len(camera_names) > 0 else 30
    print(f"每个摄像头截图间隔: {camera_interval:.1f}秒")

    # 主监控循环：使用线程安全的方式切换摄像头并截图
    while monitoring_active:
        try:
            for camera_index, camera_name in enumerate(camera_names):
                if not monitoring_active:
                    break

                # 使用锁确保标签页切换的原子性
                with lock:
                    print(f"\n🎯 作业{job_name} - 切换到摄像头 {camera_index+1}/{len(camera_names)}: '{camera_name}'")

                    # 步骤1: 确保切换到正确的标签页
                    try:
                        driver = global_driver
                        wait = WebDriverWait(driver, 10)
                        driver.switch_to.window(tab)

                        # 验证当前URL
                        current_url = driver.current_url
                        if job_url not in current_url:
                            print(f"⚠️ URL不匹配，重新加载: 期望包含{job_url}, 实际{current_url}")
                            driver.get(job_url)
                            time.sleep(3)

                        # 标签页切换后立即进行滚动复位，确保页面处于初始状态
                        print(f"🔄 作业{job_name} - 标签页切换后进行滚动复位")

                        # 获取切换前的滚动位置
                        scroll_x_before_switch = driver.execute_script('return window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;')
                        scroll_y_before_switch = driver.execute_script('return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;')
                        print(f"作业{job_name} - 标签页切换前滚动位置: ({scroll_x_before_switch}, {scroll_y_before_switch})")

                        # 强制滚动复位
                        driver.execute_script("window.scrollTo(0, 0);")
                        time.sleep(1)  # 增加等待时间确保滚动完成

                        # 验证复位结果
                        scroll_x_after_switch = driver.execute_script('return window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;')
                        scroll_y_after_switch = driver.execute_script('return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;')
                        print(f"作业{job_name} - 标签页切换后滚动位置: ({scroll_x_after_switch}, {scroll_y_after_switch})")

                        # 如果复位失败，尝试多次
                        if scroll_x_after_switch != 0 or scroll_y_after_switch != 0:
                            print(f"⚠️ 作业{job_name} - 首次滚动复位失败，尝试强制复位")
                            for attempt in range(3):
                                driver.execute_script("document.documentElement.scrollTop = 0; document.body.scrollTop = 0;")
                                driver.execute_script("window.scrollTo(0, 0);")
                                time.sleep(0.5)

                                scroll_x_check = driver.execute_script('return window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;')
                                scroll_y_check = driver.execute_script('return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;')
                                print(f"作业{job_name} - 强制复位尝试 {attempt+1}: ({scroll_x_check}, {scroll_y_check})")

                                if scroll_x_check == 0 and scroll_y_check == 0:
                                    print(f"✅ 作业{job_name} - 强制复位成功")
                                    break
                            else:
                                print(f"❌ 作业{job_name} - 强制复位失败，继续执行")

                    except Exception as tab_error:
                        print(f"❌ 切换标签页失败: {str(tab_error)}")
                        continue

                    # 步骤2: 切换摄像头
                    print(f"🔄 作业{job_name} - 开始切换到摄像头 '{camera_name}' (索引: {camera_index})")
                    success = switch_to_camera_safe(driver, wait, camera_index, camera_name, job_name)
                    if not success:
                        print(f"❌ 作业{job_name} - 切换到摄像头 '{camera_name}' 失败，跳过")
                        continue

                    # 步骤3: 等待5秒让摄像头完全加载
                    print(f"⏱️ 作业{job_name} - 等待5秒让摄像头 '{camera_name}' 完全加载...")
                    time.sleep(5)

                    # 步骤4: 截图并分析
                    print(f"📸 作业{job_name} - 开始截图摄像头 '{camera_name}'")
                    screenshot_success = capture_and_analyze_camera_safe(
                        driver, wait, job_name, job_location, camera_name, camera_index
                    )

                    if screenshot_success:
                        print(f"✅ 作业{job_name} - 摄像头 '{camera_name}' 截图完成")
                    else:
                        print(f"❌ 作业{job_name} - 摄像头 '{camera_name}' 截图失败")

                # 步骤5: 等待计算出的间隔时间
                if monitoring_active and camera_index < len(camera_names) - 1:
                    print(f"⏱️ 等待 {camera_interval:.1f}秒 后切换下一个摄像头...")
                    time.sleep(camera_interval)

            # 完成一轮所有摄像头后，等待剩余时间确保30秒周期
            if monitoring_active:
                remaining_time = 30 - (len(camera_names) * camera_interval)
                if remaining_time > 0:
                    print(f"🔄 作业{job_name} - 完成一轮监控，等待 {remaining_time:.1f}秒 后开始下一轮...")
                    time.sleep(remaining_time)
                else:
                    print(f"🔄 作业{job_name} - 完成一轮监控，立即开始下一轮...")

        except Exception as e:
            print(f"作业{job_name} - 监控循环出错: {str(e)}")
            time.sleep(10)  # 出错后等待10秒再重试

def switch_to_camera_safe(driver, wait, camera_index, camera_name, job_name):
    """线程安全的摄像头切换函数"""
    try:
        print(f"🔍 作业{job_name} - 开始切换摄像头流程")

        # 点击摄像头选择下拉框
        print(f"📋 作业{job_name} - 查找摄像头选择下拉框")
        camera_select_input = wait.until(EC.element_to_be_clickable((By.XPATH,
            '//*[@id="app"]/div[1]/div[3]/section/div/main/div[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div/input')))
        print(f"📋 作业{job_name} - 点击摄像头选择下拉框")
        driver.execute_script("arguments[0].click();", camera_select_input)
        time.sleep(2)

        # 等待下拉列表出现
        print(f"📋 作业{job_name} - 等待下拉列表出现")
        ul_element = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR,
            "ul.el-scrollbar__view.el-select-dropdown__list")))
        camera_elements = ul_element.find_elements(By.CSS_SELECTOR, "li.el-select-dropdown__item")
        print(f"📋 作业{job_name} - 找到 {len(camera_elements)} 个摄像头选项")

        # 显示所有摄像头选项
        for idx, element in enumerate(camera_elements):
            try:
                span_element = element.find_element(By.TAG_NAME, "span")
                option_name = span_element.text.strip()
                print(f"📋 作业{job_name} - 选项 {idx}: '{option_name}'")
            except:
                print(f"📋 作业{job_name} - 选项 {idx}: 无法获取名称")

        if camera_index < len(camera_elements):
            target_camera = camera_elements[camera_index]

            # 获取目标摄像头名称进行验证
            try:
                span_element = target_camera.find_element(By.TAG_NAME, "span")
                target_name = span_element.text.strip()
            except:
                target_name = target_camera.text.strip()

            print(f"🎯 作业{job_name} - 点击摄像头索引 {camera_index}: '{target_name}'")
            driver.execute_script("arguments[0].click();", target_camera)
            time.sleep(2)

            # 验证切换是否成功
            try:
                print(f"🔍 作业{job_name} - 验证摄像头切换结果")
                current_input = driver.find_element(By.XPATH,
                    '//*[@id="app"]/div[1]/div[3]/section/div/main/div[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div/input')
                current_value = current_input.get_attribute("value") or ""

                if target_name in current_value or current_value in target_name:
                    print(f"✅ 作业{job_name} - 摄像头切换成功: '{current_value}'")
                    return True
                else:
                    print(f"❌ 作业{job_name} - 摄像头切换验证失败: 期望包含'{target_name}', 实际'{current_value}'")
                    return False
            except Exception as verify_error:
                print(f"❌ 作业{job_name} - 验证摄像头切换失败: {str(verify_error)}")
                return False
        else:
            print(f"❌ 作业{job_name} - 摄像头索引 {camera_index} 超出范围，当前有 {len(camera_elements)} 个摄像头")
            return False

    except Exception as e:
        print(f"❌ 作业{job_name} - 切换摄像头失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def capture_and_analyze_camera_safe(driver, wait, job_name, job_location, camera_name, camera_index):
    """线程安全的截图并分析摄像头画面"""
    try:
        # 创建摄像头专用目录
        safe_dir_name = sanitize_filename(f"{job_name}_{camera_name}")
        camera_dir = os.path.join(SCREENSHOTS_DIR, safe_dir_name)
        os.makedirs(camera_dir, exist_ok=True)

        # 生成时间戳和文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_camera_name = sanitize_filename(camera_name)
        meaningful_filename = f"{timestamp}_{safe_camera_name}.png"
        filepath = os.path.join(camera_dir, meaningful_filename)

        # 查找播放器元素 - 使用初代脚本的方法
        target_element = None
        player_selectors = [
            (By.XPATH, '//*[@id="player"]'),  # 初代脚本使用的选择器
            (By.CSS_SELECTOR, "canvas.play-window"),
            (By.CSS_SELECTOR, "video.play-window"),
            (By.CSS_SELECTOR, "[id*='player']")
        ]

        print(f"作业{job_name} - 开始查找播放器元素")
        for i, (selector_type, selector) in enumerate(player_selectors):
            try:
                print(f"作业{job_name} - 尝试选择器 {i+1}: {selector}")
                target_element = wait.until(EC.presence_of_element_located((selector_type, selector)))
                print(f"作业{job_name} - ✅ 成功找到播放器元素，使用选择器: {selector}")
                break
            except Exception as e:
                print(f"作业{job_name} - ❌ 选择器 {i+1} 失败: {str(e)}")
                continue

        if target_element is None:
            print(f"作业{job_name} - ❌ 所有选择器都失败，找不到播放器元素")
            return False

        # 使用初代脚本的滚动定位方法
        print(f"作业{job_name} - 开始滚动定位摄像头元素")

        # 步骤1: 获取滚动前的状态
        scroll_x_before = driver.execute_script(
            'return window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;')
        scroll_y_before = driver.execute_script(
            'return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;')
        print(f"作业{job_name} - 滚动前位置: ({scroll_x_before}, {scroll_y_before})")

        # 步骤2: 滚动到目标元素位置
        print(f"作业{job_name} - 执行scrollIntoView")

        # 获取元素在页面中的位置
        element_rect = driver.execute_script("""
            var rect = arguments[0].getBoundingClientRect();
            var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            var scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
            return {
                top: rect.top + scrollTop,
                left: rect.left + scrollLeft,
                bottom: rect.bottom + scrollTop,
                right: rect.right + scrollLeft,
                width: rect.width,
                height: rect.height
            };
        """, target_element)
        print(f"作业{job_name} - 元素在页面中的绝对位置: {element_rect}")

        # 执行滚动，确保元素可见
        driver.execute_script("arguments[0].scrollIntoView({behavior: 'instant', block: 'center'});", target_element)
        time.sleep(1)  # 等待滚动完成

        # 步骤3: 获取滚动后的状态
        scroll_x_after = driver.execute_script(
            'return window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;')
        scroll_y_after = driver.execute_script(
            'return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;')
        print(f"作业{job_name} - 滚动后位置: ({scroll_x_after}, {scroll_y_after})")

        # 验证滚动是否发生
        scroll_occurred = (scroll_x_after != scroll_x_before) or (scroll_y_after != scroll_y_before)
        if scroll_occurred:
            print(f"✅ 作业{job_name} - 滚动已发生，偏移量: ({scroll_x_after - scroll_x_before}, {scroll_y_after - scroll_y_before})")
        else:
            print(f"ℹ️ 作业{job_name} - 元素已在视口内，无需滚动")

        # 步骤4: 获取元素位置和大小
        location = target_element.location
        size = target_element.size
        scale = driver.execute_script('return window.devicePixelRatio || 1;')
        print(f"作业{job_name} - 元素位置: {location}, 大小: {size}, 缩放: {scale}")

        # 步骤5: 计算真实坐标（考虑滚动偏移）
        left = (location['x'] + scroll_x_after) * scale
        top = (location['y'] + scroll_y_after) * scale
        right = left + (size['width'] * scale)
        bottom = top + (size['height'] * scale)
        print(f"作业{job_name} - 计算后坐标: left={left}, top={top}, right={right}, bottom={bottom}")

        print(f"作业{job_name} - 准备截图 {camera_name}: 位置={location}, 大小={size}, 滚动=({scroll_x_after},{scroll_y_after}), 缩放={scale}")

        # 保存整页截图
        fullpage_filename = f"{timestamp}_{safe_camera_name}_fullpage.png"
        fullpage_filepath = os.path.join(camera_dir, fullpage_filename)
        driver.save_screenshot(fullpage_filepath)
        print(f"作业{job_name} - 整页截图已保存: {fullpage_filepath}")

        # 裁剪摄像头区域
        from PIL import Image
        with Image.open(fullpage_filepath) as img:
            # 调整裁剪区域，向下偏移100像素避免截取到界面元素
            adjusted_top = top + 100
            cropped_img = img.crop((left, adjusted_top, right, bottom))
            cropped_img.save(filepath)
            print(f"作业{job_name} - 裁剪后图像已保存: {filepath}")

        # 步骤6: 滚动复位，防止影响下次截图
        print(f"作业{job_name} - 开始滚动复位")

        # 获取复位前的位置
        scroll_x_before_reset = driver.execute_script(
            'return window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;')
        scroll_y_before_reset = driver.execute_script(
            'return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;')
        print(f"作业{job_name} - 复位前位置: ({scroll_x_before_reset}, {scroll_y_before_reset})")

        # 执行多种方式的滚动复位，确保成功
        reset_methods = [
            "window.scrollTo(0, 0);",
            "document.documentElement.scrollTop = 0; document.body.scrollTop = 0;",
            "window.scrollTo({top: 0, left: 0, behavior: 'instant'});"
        ]

        for i, method in enumerate(reset_methods):
            print(f"作业{job_name} - 尝试复位方法 {i+1}: {method}")
            driver.execute_script(method)
            time.sleep(0.5)

            # 验证复位结果
            scroll_x_after_reset = driver.execute_script(
                'return window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;')
            scroll_y_after_reset = driver.execute_script(
                'return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;')
            print(f"作业{job_name} - 复位方法 {i+1} 结果: ({scroll_x_after_reset}, {scroll_y_after_reset})")

            if scroll_x_after_reset == 0 and scroll_y_after_reset == 0:
                print(f"✅ 作业{job_name} - 滚动复位成功 (方法 {i+1})")
                break
        else:
            print(f"⚠️ 作业{job_name} - 所有复位方法都失败，当前位置: ({scroll_x_after_reset}, {scroll_y_after_reset})")

        # 分析图像
        status = check_camera_status(filepath)

        # 保存元数据
        metadata_file = os.path.join(camera_dir, f"info_{timestamp}.json")
        metadata = {
            "job_name": job_name,
            "job_location": job_location,
            "camera_name": camera_name,
            "camera_index": camera_index,
            "timestamp": timestamp,
            "filename": meaningful_filename,
            "path": filepath,
            "status": "异常" if status == 0 else "正常"
        }

        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

        # 如果图像异常，记录到全局列表
        if status == 0 or DEBUG_MODE:
            abnormal_images.append({
                "filepath": os.path.basename(filepath),
                "filepath_full": filepath,
                "dir": safe_dir_name,
                "job_name": job_name,
                "job_location": job_location,
                "camera_name": camera_name,
                "timestamp": timestamp,
                "reason": "摄像头黑屏" if status == 0 else "调试模式"
            })

        return True

    except Exception as e:
        print(f"作业{job_name} - 截图和分析失败: {str(e)}")
        return False

def start_monitoring_threads(job_tabs):
    """为每个作业标签页启动一个监控线程"""
    global monitoring_threads

    for tab_info in job_tabs:
        thread = threading.Thread(
            target=monitor_job_cameras,
            args=(tab_info,)
        )
        thread.daemon = True
        monitoring_threads.append(thread)
        thread.start()

# 旧的monitor_camera函数已删除，现在使用monitor_job_cameras函数



def check_camera_status(image_path, debug=False):
    """检测摄像头是否正常开启"""
    print(f"开始分析图像: {image_path}")
    if debug:
        print("调试模式已启用")

    # 尝试使用PIL库读取图像，而不是OpenCV（处理中文路径更好）
    try:
        from PIL import Image
        pil_image = Image.open(image_path)
        # 转换为OpenCV格式
        image = np.array(pil_image)
        # 如果是RGB图像，转换为BGR (OpenCV使用BGR)
        if len(image.shape) == 3 and image.shape[2] == 3:
            image = image[:, :, ::-1].copy()  # RGB to BGR
        print(f"使用PIL成功读取图像，尺寸: {image.shape}")
    except Exception as pil_error:
        print(f"使用PIL读取图像失败: {str(pil_error)}")
        # 回退到OpenCV
        try:
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    image = cv2.imread(image_path)
                    if image is not None:
                        print(f"图像成功读取，尺寸: {image.shape}")
                        break
                    if attempt < max_attempts - 1:
                        print(f"尝试读取图像 {image_path} 失败，第 {attempt + 1} 次重试...")
                        time.sleep(1)
                except Exception as e:
                    print(f"读取图像出错 (尝试 {attempt+1}/{max_attempts}): {str(e)}")
                    if attempt < max_attempts - 1:
                        time.sleep(1)
            else:
                print(f"无法读取图像: {image_path}")
                # 返回错误代码而不是直接返回-1，这样调用者可以知道发生了错误
                raise Exception(f"无法读取图像: {image_path}")
        except Exception as cv_error:
            print(f"OpenCV读取失败: {str(cv_error)}")
            raise Exception(f"无法读取图像: {image_path}")

    try:
        # 保存原始图像的副本用于调试
        if DEBUG_MODE:
            # 确保debug文件保存在正确的目录中，避免中文乱码
            image_dir = os.path.dirname(image_path)
            image_basename = os.path.basename(image_path)
            debug_filename = image_basename.replace(".png", "_debug.png")
            debug_copy = os.path.join(image_dir, debug_filename)

            # 使用cv2.imencode避免中文路径问题
            success, encoded_img = cv2.imencode('.png', image)
            if success:
                with open(debug_copy, 'wb') as f:
                    f.write(encoded_img.tobytes())
                print(f"已保存调试用图像副本: {debug_copy}")
            else:
                print(f"保存调试图像失败: {debug_copy}")
        
        height, width = image.shape[:2]
        print(f"图像尺寸: {width} x {height}")
        
        # 如果图像太小，可能是裁剪错误
        if width < 20 or height < 20:
            print(f"图像尺寸过小: {width} x {height}")
            raise Exception("图像尺寸过小，可能是裁剪错误")
        
        x_start, y_start = int(width * 0.1), int(height * 0.1)
        x_end, y_end = int(width * 0.9), int(height * 0.9)
        center_region = image[y_start:y_end, x_start:x_end]
        
        if center_region.size == 0:
            print("中心区域为空")
            raise Exception("中心区域为空")
        
        gray = cv2.cvtColor(center_region, cv2.COLOR_BGR2GRAY)
        avg_brightness = np.mean(gray)
        std_brightness = np.std(gray)
        non_black_ratio = np.sum(gray > 10) / (gray.shape[0] * gray.shape[1])
        color_std = np.std(center_region, axis=(0, 1)).mean()
        edges = cv2.Canny(gray, 50, 150)
        edge_count = np.count_nonzero(edges) / (edges.shape[0] * edges.shape[1])
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        hist_norm = hist / hist.sum()
        dominant_color_ratio = np.max(hist_norm)
        
        print(f"图像分析结果: avg_brightness={avg_brightness:.2f}, std_brightness={std_brightness:.2f}, "
              f"non_black_ratio={non_black_ratio:.2f}, color_std={color_std:.2f}, edge_count={edge_count:.2f}, "
              f"dominant_color_ratio={dominant_color_ratio:.2f}")

        is_closed = False
        if avg_brightness < 15 and std_brightness < 10:
            is_closed = True
            print("判定为异常: 亮度过低且标准差小")
        elif dominant_color_ratio > 0.95 and edge_count < 0.01:
            is_closed = True
            print("判定为异常: 主导颜色比例过高且边缘数量少")
        elif std_brightness < 0.5 and color_std < 0.5:
            is_closed = True
            print("判定为异常: 亮度标准差和颜色标准差都过小")

        print(f"{image_path}的识别结果：{is_closed}")
        
        # 如果处于调试模式，保存带有参数的调试信息
        if DEBUG_MODE:
            # 在原图上添加文字
            debug_img = image.copy()
            info_text = [
                f"Brightness: {avg_brightness:.1f} (std: {std_brightness:.1f})",
                f"Non-black: {non_black_ratio:.2f}",
                f"Color std: {color_std:.2f}",
                f"Edge count: {edge_count:.2f}",
                f"Dom. color: {dominant_color_ratio:.2f}",
                f"Status: {'Abnormal' if is_closed else 'Normal'}"
            ]
            
            y_pos = 30
            for text in info_text:
                cv2.putText(debug_img, text, (10, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 
                           0.7, (0, 0, 255), 2, cv2.LINE_AA)
                y_pos += 30
            
            # 确保analyzed文件保存在正确的目录中，避免中文乱码
            image_dir = os.path.dirname(image_path)
            image_basename = os.path.basename(image_path)
            analyzed_filename = image_basename.replace(".png", "_analyzed.png")
            debug_path = os.path.join(image_dir, analyzed_filename)

            # 使用cv2.imencode避免中文路径问题
            success, encoded_img = cv2.imencode('.png', debug_img)
            if success:
                with open(debug_path, 'wb') as f:
                    f.write(encoded_img.tobytes())
                print(f"已保存分析后的图像: {debug_path}")
            else:
                print(f"保存分析图像失败: {debug_path}")

        return 0 if is_closed else 1
        
    except Exception as e:
        print(f"图像分析过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        # 返回错误，这样调用者可以知道发生了错误
        raise Exception(f"图像分析失败: {str(e)}")

# 启动应用
if __name__ == '__main__':
    # 确保目录存在
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    os.makedirs('static/img', exist_ok=True)
    os.makedirs(SCREENSHOTS_DIR, exist_ok=True)
    
    # 创建错误图像占位符
    create_error_image()
    
    app.run(debug=True, port=5000)



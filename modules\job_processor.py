"""
作业处理模块
提供作业信息获取、标签页创建等功能
"""

import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from typing import List, Dict, Any, Optional

from .config import Config, global_state
from .browser_manager import BrowserManager


class JobProcessor:
    """作业处理类"""
    
    def __init__(self, browser_manager: BrowserManager):
        self.browser_manager = browser_manager
    
    def process_jobs(self) -> List[Dict[str, Any]]:
        """
        处理所有作业，创建标签页（与原版本完全一致）

        Returns:
            List[Dict[str, Any]]: 作业标签页信息列表
        """
        try:
            driver = global_state.global_driver
            wait = WebDriverWait(driver, 20)
            job_tabs = []

            # 访问目标页面
            driver.get(Config.TARGET_URL)

            # 等待页面加载完成
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

            try:
                # 点击分页设置（与原版本一致）
                pagination_dropdown = wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//div[contains(@class,'pagination')]//input"))
                )
                driver.execute_script("arguments[0].click();", pagination_dropdown)

                # 选择50条/页
                select_option = wait.until(
                    EC.visibility_of_element_located((By.XPATH, "//li[contains(.,'50条/页')]"))
                )
                driver.execute_script("arguments[0].click();", select_option)

                # 等待数据加载
                wait.until(
                    EC.invisibility_of_element_located((By.CSS_SELECTOR, ".el-loading-mask"))
                )

                # 定位tbody并获取所有行
                tbody = wait.until(EC.presence_of_element_located((By.XPATH, "//tbody")))

                # 获取所有"作业中"的行索引（与原版本一致）
                job_in_progress_indexes = []
                rows = tbody.find_elements(By.XPATH, "./tr")
                for index, row in enumerate(rows, start=1):  # 索引从1开始
                    try:
                        # 获取最后一个td的状态
                        last_td = row.find_element(By.XPATH, "./td[last()]")
                        status = last_td.text.strip()

                        if status == "作业中":
                            job_in_progress_indexes.append(index)

                    except NoSuchElementException:
                        continue  # 跳过异常行

                # 添加无任务时的提示
                if not job_in_progress_indexes:
                    print("当前没有正在工作中的任务")
                    return []

                # 根据二级公司选项确定要使用的td索引（与原版本一致）
                td_index = 4 if global_state.is_secondary_company else 3

                # 为每个作业创建一个标签页
                main_window = driver.current_window_handle

                for row_index in job_in_progress_indexes:
                    try:
                        print(f"处理第{row_index}行作业")

                        # 重新获取tbody和所有行
                        tbody = wait.until(EC.presence_of_element_located((By.XPATH, "//tbody")))
                        rows = tbody.find_elements(By.XPATH, "./tr")
                        row = rows[row_index - 1]

                        # 获取作业名称和地点信息用于后续使用（与原版本一致）
                        try:
                            job_name_td = row.find_element(By.XPATH, f"./td[2]")
                            job_name = job_name_td.text.strip()
                            job_location_td = row.find_element(By.XPATH, f"./td[3]")
                            job_location = job_location_td.text.strip()
                        except Exception as e:
                            print(f"获取作业信息失败: {e}")
                            job_name = f"作业{row_index}"
                            job_location = "未知地点"

                        # 根据二级公司选项获取对应列
                        target_td = row.find_element(By.XPATH, f"./td[{td_index}]")
                        link = target_td.find_element(By.XPATH, ".//a")

                        # 点击链接打开作业详情（与原版本一致）
                        try:
                            driver.execute_script("arguments[0].click();", link)
                            wait.until(EC.presence_of_element_located((By.XPATH, '//div[contains(@class,"app-container")]')))
                        except Exception as e:
                            print(f"打开作业详情失败: {e}")
                            continue

                        # 等待作业详情页面加载
                        time.sleep(3)

                        # 获取作业类别
                        job_category = self._get_job_category(driver, wait, job_name)

                        # 获取摄像头列表（与原版本一致）
                        camera_names = self._get_camera_names_original_method(driver, wait)

                        if not camera_names:
                            print(f"作业 {job_name} 没有摄像头，跳过")
                            driver.back()
                            time.sleep(2)
                            continue

                        # 记录作业信息（包含作业类别）
                        job_info = {
                            "job_name": job_name,
                            "job_location": job_location,
                            "job_category": job_category,
                            "cameras": camera_names
                        }
                        global_state.job_info_list.append(job_info)
                        print(f"✅ 作业信息已保存: {job_name}, 作业类别: {job_category}, 摄像头数量: {len(camera_names)}")

                        # 获取当前URL（作业页面URL）
                        job_page_url = driver.current_url
                        print(f"作业页面URL: {job_page_url}")

                        # 关闭摄像头选择下拉框（如果还开着）
                        try:
                            driver.execute_script("document.body.click();")
                            time.sleep(1)
                        except:
                            pass

                        # 创建作业标签页
                        tab_info = self._create_job_tab_original_method(driver, job_name, job_location, camera_names, job_page_url)
                        if tab_info:
                            job_tabs.append(tab_info)

                        # 返回主窗口（与原版本一致）
                        print("8. 切回主窗口")
                        driver.switch_to.window(main_window)
                        time.sleep(2)  # 等待2秒，与原版本一致

                        # 返回主页面并重新设置分页（与原版本一致）
                        print("9. 返回主页面并重新设置分页")
                        driver.get(Config.TARGET_URL)

                        # 等待页面加载
                        wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

                        # 重新设置分页
                        pagination_dropdown = wait.until(
                            EC.element_to_be_clickable((By.XPATH, "//div[contains(@class,'pagination')]//input"))
                        )
                        driver.execute_script("arguments[0].click();", pagination_dropdown)

                        select_option = wait.until(
                            EC.visibility_of_element_located((By.XPATH, "//li[contains(.,'50条/页')]"))
                        )
                        driver.execute_script("arguments[0].click();", select_option)

                        # 等待数据加载
                        wait.until(
                            EC.invisibility_of_element_located((By.CSS_SELECTOR, ".el-loading-mask"))
                        )

                    except Exception as e:
                        print(f"处理作业 {row_index} 时出错: {str(e)}")
                        # 尝试返回主窗口
                        try:
                            driver.switch_to.window(main_window)
                        except:
                            pass
                        continue

            except Exception as e:
                print(f"处理作业列表时出错: {str(e)}")
                return []

            print(f"成功创建 {len(job_tabs)} 个作业标签页")
            return job_tabs

        except Exception as e:
            print(f"处理作业时出错: {str(e)}")
            return []

    def _get_camera_names_original_method(self, driver, wait) -> List[str]:
        """获取摄像头名称（与原版本完全一致）"""
        try:
            # 点击打开摄像头列表
            camera_select_input = wait.until(EC.element_to_be_clickable((By.XPATH,
                '//*[@id="app"]/div[1]/div[3]/section/div/main/div[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div/input')))
            driver.execute_script("arguments[0].click();", camera_select_input)
            time.sleep(1)  # 等待下拉列表展开

            # 等待摄像头列表出现，使用更精确的选择器
            ul_element = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR,
                "ul.el-scrollbar__view.el-select-dropdown__list")))
            camera_elements = ul_element.find_elements(By.CSS_SELECTOR, "li.el-select-dropdown__item")
            camera_names = []

            print(f"找到 {len(camera_elements)} 个摄像头选项")

            # 确保正确获取摄像头名称（与原版本一致）
            for i, li in enumerate(camera_elements):
                try:
                    # 尝试多种方式获取摄像头名称
                    name = None

                    # 方法1: 直接查找span元素
                    try:
                        span_element = li.find_element(By.TAG_NAME, "span")
                        name = span_element.text.strip()
                    except:
                        pass

                    # 方法2: 如果span方法失败，尝试获取li的文本内容
                    if not name:
                        name = li.text.strip()

                    # 方法3: 如果还是没有，使用innerHTML
                    if not name:
                        name = li.get_attribute("innerHTML")
                        if name:
                            # 简单清理HTML标签
                            import re
                            name = re.sub(r'<[^>]+>', '', name).strip()

                    if name and name != "":
                        print(f"发现摄像头 {i+1}: '{name}'")
                        camera_names.append(name)
                    else:
                        fallback_name = f"摄像头_{i+1}"
                        print(f"摄像头 {i+1} 名称为空，使用备用名称: '{fallback_name}'")
                        camera_names.append(fallback_name)

                except Exception as e:
                    fallback_name = f"未命名摄像头_{len(camera_names)+1}"
                    print(f"获取摄像头 {i+1} 名称失败: {str(e)}，使用备用名称: '{fallback_name}'")
                    camera_names.append(fallback_name)

            return camera_names

        except Exception as e:
            print(f"获取摄像头列表失败: {str(e)}")
            return []

    def _get_job_category(self, driver, wait: WebDriverWait, job_name: str) -> str:
        """
        获取作业类别

        Args:
            driver: WebDriver实例
            wait: WebDriverWait实例
            job_name: 作业名称（用于日志）

        Returns:
            str: 作业类别，多个类别用英文逗号分隔
        """
        try:
            # 使用提供的xpath获取作业类别
            category_xpath = '//*[@id="app"]/div[1]/div[3]/section/div/main/div[1]/div[1]/div[1]/div[2]/div[2]/div/table/tbody[3]/tr/td/div/span[2]'

            # 等待元素出现
            category_element = wait.until(
                EC.presence_of_element_located((By.XPATH, category_xpath))
            )

            job_category = category_element.text.strip()

            if job_category:
                print(f"📋 作业{job_name} - 获取到作业类别: {job_category}")
                return job_category
            else:
                print(f"⚠️ 作业{job_name} - 作业类别为空")
                return "未知类别"

        except Exception as e:
            print(f"❌ 作业{job_name} - 获取作业类别失败: {str(e)}")
            # 尝试其他可能的xpath
            try:
                # 备用xpath，查找包含作业类别的span元素
                backup_elements = driver.find_elements(By.XPATH, "//span[contains(text(),'作业') or contains(text(),'高处') or contains(text(),'动火') or contains(text(),'起重')]")

                for element in backup_elements:
                    text = element.text.strip()
                    if any(keyword in text for keyword in ["高处作业", "动火作业", "大型起重作业"]):
                        print(f"📋 作业{job_name} - 通过备用方法获取到作业类别: {text}")
                        return text

                print(f"⚠️ 作业{job_name} - 备用方法也未找到作业类别")
                return "未知类别"

            except Exception as backup_e:
                print(f"❌ 作业{job_name} - 备用方法也失败: {str(backup_e)}")
                return "未知类别"

    def _create_job_tab_original_method(self, driver, job_name: str, job_location: str,
                                      camera_names: List[str], job_page_url: str) -> Optional[Dict[str, Any]]:
        """创建作业标签页（与原版本一致）"""
        try:
            print(f"\n=== 为作业 {job_name} 创建标签页（包含 {len(camera_names)} 个摄像头）===")

            # 步骤2: 打开新标签页（与原版本一致）
            print("2. 创建作业标签页")
            driver.execute_script("window.open('about:blank');")
            time.sleep(2)  # 等待2秒

            # 步骤3: 获取新标签页句柄
            job_tab = driver.window_handles[-1]
            print(f"3. 作业标签页句柄: {job_tab}")

            # 步骤4: 切换到新标签页
            print("4. 切换到作业标签页")
            driver.switch_to.window(job_tab)
            time.sleep(2)  # 等待2秒

            # 步骤5: 导航到作业页面
            print("5. 导航到作业页面")
            driver.get(job_page_url)
            time.sleep(3)  # 等待页面加载

            # 步骤6: 等待页面完全加载
            print("6. 等待页面完全加载")
            wait = WebDriverWait(driver, 20)
            wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="app"]')))
            time.sleep(2)  # 等待2秒

            # 步骤7: 验证摄像头列表
            print("7. 验证摄像头列表")

            tab_info = {
                "tab": job_tab,
                "job_name": job_name,
                "job_location": job_location,
                "camera_names": camera_names,
                "url": job_page_url
            }

            print(f"✅ 作业标签页创建完成 - 作业: {job_name}, 摄像头数量: {len(camera_names)}")

            return tab_info

        except Exception as e:
            print(f"创建作业标签页失败: {str(e)}")
            return None




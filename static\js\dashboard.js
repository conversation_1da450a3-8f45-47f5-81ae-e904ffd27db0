document.addEventListener('DOMContentLoaded', function() {
    // DOM元素
    const loadingModal = document.getElementById('loading-modal');
    const imageList = document.getElementById('image-list');
    const jobList = document.getElementById('job-list');
    const currentImage = document.getElementById('current-image');
    const imageJobName = document.getElementById('image-job-name');
    const imageJobLocation = document.getElementById('image-job-location');
    const cameraNamElement = document.getElementById('image-camera-name');
    const imageTimestamp = document.getElementById('image-timestamp');
    const imageReason = document.getElementById('image-reason');
    const prevImageBtn = document.getElementById('prev-image');
    const nextImageBtn = document.getElementById('next-image');
    const applyFilterBtn = document.getElementById('apply-filter');
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const stopMonitoringBtn = document.getElementById('stop-monitoring');
    const refreshDataBtn = document.getElementById('refresh-data');

    // 灯箱相关DOM元素
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightbox-image');
    const lightboxClose = document.querySelector('.lightbox-close');
    const lightboxPrev = document.getElementById('lightbox-prev');
    const lightboxNext = document.getElementById('lightbox-next');
    const lightboxJobName = document.getElementById('lightbox-job-name');
    const lightboxJobLocation = document.getElementById('lightbox-job-location');
    const lightboxCameraName = document.getElementById('lightbox-camera-name');
    const lightboxTimestamp = document.getElementById('lightbox-timestamp');
    const lightboxJobCategory = document.getElementById('lightbox-job-category');
    const lightboxDangerStatus = document.getElementById('lightbox-danger-status');
    const lightboxDangerTypes = document.getElementById('lightbox-danger-types');
    
    // 全局变量
    let abnormalImages = [];
    let currentImageIndex = -1;
    let pollingInterval = null;

    // 灯箱缩放相关变量
    let currentScale = 1;
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let translateX = 0;
    let translateY = 0;
    
    // 初始化日期选择器
    const today = new Date();
    const todayStr = formatDateForInput(today);
    startDateInput.value = todayStr;
    endDateInput.value = todayStr;
    
    // 初始化
    initPage();
    
    // 事件监听
    prevImageBtn.addEventListener('click', showPreviousImage);
    nextImageBtn.addEventListener('click', showNextImage);
    applyFilterBtn.addEventListener('click', loadAbnormalImages);
    stopMonitoringBtn.addEventListener('click', stopMonitoring);
    refreshDataBtn.addEventListener('click', refreshData);

    // 灯箱事件监听
    currentImage.addEventListener('click', openLightbox);
    lightboxClose.addEventListener('click', closeLightbox);
    lightboxPrev.addEventListener('click', showLightboxPrevious);
    lightboxNext.addEventListener('click', showLightboxNext);
    lightbox.addEventListener('click', function(e) {
        if (e.target === lightbox) {
            closeLightbox();
        }
    });

    // 灯箱图片缩放和拖拽事件
    lightboxImage.addEventListener('wheel', handleImageZoom);
    lightboxImage.addEventListener('click', handleImageClick);
    lightboxImage.addEventListener('mousedown', handleMouseDown);
    lightboxImage.addEventListener('mousemove', handleMouseMove);
    lightboxImage.addEventListener('mouseup', handleMouseUp);
    lightboxImage.addEventListener('mouseleave', handleMouseUp);

    // 键盘事件监听
    document.addEventListener('keydown', function(e) {
        if (lightbox.style.display === 'flex') {
            if (e.key === 'Escape') {
                closeLightbox();
            } else if (e.key === 'ArrowLeft') {
                showLightboxPrevious();
            } else if (e.key === 'ArrowRight') {
                showLightboxNext();
            }
        }
    });
    
    /**
     * 初始化页面
     */
    function initPage() {
        showLoading();
        
        // 加载作业信息
        loadJobInfo();
        
        // 加载异常图片
        loadAbnormalImages();
        
        // 开始实时数据轮询
        startPolling();
    }
    
    /**
     * 开始实时数据轮询
     */
    function startPolling() {
        // 清除可能存在的旧轮询
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }
        
        // 每5秒轮询一次最新数据
        pollingInterval = setInterval(function() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    // 更新异常图片列表
                    if (data.abnormal_images && data.abnormal_images.length > 0) {
                        // 将新图片添加到列表
                        for (let newImage of data.abnormal_images) {
                            // 检查图片是否已存在
                            const exists = abnormalImages.some(img => img.filepath === newImage.filepath);
                            if (!exists) {
                                abnormalImages.unshift(newImage); // 添加到开头
                            }
                        }
                        // 更新图片列表显示
                        renderImageList();
                        
                        // 如果未选中图片，显示最新的图片
                        if (currentImageIndex === -1 && abnormalImages.length > 0) {
                            showImage(0);
                        }
                    }
                })
                .catch(error => console.error('数据轮询出错:', error));
        }, 5000);
    }
    
    /**
     * 加载异常图片列表
     */
    function loadAbnormalImages() {
        showLoading();

        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        const dangerFilter = document.getElementById('danger-filter') ? document.getElementById('danger-filter').value : 'all';
        const hasDangerOnly = document.getElementById('danger-only') ? document.getElementById('danger-only').checked : false;
        const timeOrder = document.getElementById('time-order') ? document.getElementById('time-order').value : 'desc';

        let url = '/api/abnormal_images';
        const params = new URLSearchParams();

        if (startDate) params.append('start_time', startDate);
        if (endDate) params.append('end_time', endDate);
        if (dangerFilter && dangerFilter !== 'all') params.append('danger_filter', dangerFilter);
        if (hasDangerOnly) params.append('has_danger_only', 'true');
        if (timeOrder) params.append('time_order', timeOrder);

        if (params.toString()) {
            url += '?' + params.toString();
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                abnormalImages = data;
                renderImageList();

                // 显示第一张图片（如果有）
                if (abnormalImages.length > 0) {
                    showImage(0);
                } else {
                    clearImageDisplay();
                }
                hideLoading();
            })
            .catch(error => {
                console.error('获取图片列表失败:', error);
                hideLoading();
            });
    }
    
    /**
     * 加载作业信息
     */
    function loadJobInfo() {
        fetch('/api/job_info')
            .then(response => response.json())
            .then(data => {
                renderJobList(data);
            })
            .catch(error => {
                console.error('获取作业信息失败:', error);
            });
    }
    
    /**
     * 渲染图片列表
     */
    function renderImageList() {
        imageList.innerHTML = '';
        
        if (abnormalImages.length === 0) {
            imageList.innerHTML = '<p class="no-data">当前没有异常图片</p>';
            return;
        }
        
        for (let i = 0; i < abnormalImages.length; i++) {
            const image = abnormalImages[i];
            const listItem = document.createElement('div');
            listItem.className = 'image-list-item';
            if (i === currentImageIndex) {
                listItem.classList.add('selected');
            }
            
            // 格式化时间戳
            const formattedTimestamp = formatTimestamp(image.timestamp);
            
            // 组装图片完整路径
            let imagePath;
            if (image.image_path) {
                // 新格式: 使用完整路径
                imagePath = `/${image.image_path}`;
            } else if (image.dir) {
                // 兼容旧格式: 使用目录
                imagePath = `/screenshots/${image.dir}/${image.filepath}`;
            } else {
                // 兼容旧格式: 直接使用文件名
                imagePath = `/screenshots/${image.filepath}`;
            }
            
            // 判断是否有危险
            const hasDanger = image.has_danger || image.is_black_screen;
            const dangerClass = hasDanger ? 'danger' : 'safe';

            // 危险类型显示
            let dangerTypes = '';
            if (image.is_black_screen) {
                dangerTypes = '黑屏';
            } else if (image.danger_types && image.danger_types.length > 0) {
                dangerTypes = image.danger_types.join(', ');
            } else {
                dangerTypes = '无危险';
            }

            listItem.className = `image-list-item ${dangerClass}`;
            listItem.innerHTML = `
                <img src="${imagePath}" alt="缩略图" class="image-list-item-thumbnail" onerror="this.src='/static/img/image_error.png'">
                <div class="image-list-item-info">
                    <h3>${image.job_name} - ${image.camera_name}</h3>
                    <p>地点: ${image.job_location}</p>
                    <p>时间: ${formattedTimestamp}</p>
                    <p class="danger-status ${dangerClass}">是否有危险: ${hasDanger ? '是' : '否'}</p>
                    <p class="danger-types">危险类型: ${dangerTypes}</p>
                </div>
            `;
            
            listItem.addEventListener('click', () => showImage(i));
            imageList.appendChild(listItem);
        }
    }
    
    /**
     * 渲染作业列表
     */
    function renderJobList(jobs) {
        jobList.innerHTML = '';
        
        if (jobs.length === 0) {
            jobList.innerHTML = '<p class="no-data">当前没有作业信息</p>';
            return;
        }
        
        for (const job of jobs) {
            const jobItem = document.createElement('div');
            jobItem.className = 'job-item';
            
            let cameraListHTML = '';
            if (job.cameras && job.cameras.length > 0) {
                cameraListHTML = '<ul class="camera-list">';
                for (const camera of job.cameras) {
                    cameraListHTML += `<li>${camera}</li>`;
                }
                cameraListHTML += '</ul>';
            }
            
            jobItem.innerHTML = `
                <h3>${job.job_name}</h3>
                <div class="job-item-info">
                    <p><strong>作业地点:</strong> ${job.job_location}</p>
                    <p><strong>作业类别:</strong> ${job.job_category || '未知类别'}</p>
                    <p><strong>摄像头数量:</strong> ${job.cameras ? job.cameras.length : 0}</p>
                </div>
                ${cameraListHTML}
            `;
            
            jobList.appendChild(jobItem);
        }
    }
    
    /**
     * 显示指定索引的图片
     */
    function showImage(index) {
        if (index < 0 || index >= abnormalImages.length) {
            return;
        }
        
        const image = abnormalImages[index];
        currentImageIndex = index;
        
        // 组装图片完整路径
        let imagePath;
        if (image.image_path) {
            // 新格式: 使用完整路径
            imagePath = `/${image.image_path}`;
        } else if (image.dir) {
            // 兼容旧格式: 使用目录
            imagePath = `/screenshots/${image.dir}/${image.filepath}`;
        } else {
            // 兼容旧格式: 直接使用文件名
            imagePath = `/screenshots/${image.filepath}`;
        }
        
        // 更新图片显示
        currentImage.src = imagePath;
        currentImage.onerror = function() {
            // 如果图片加载失败，显示错误消息
            this.onerror = null;
            this.src = '/static/img/image_error.png';
            this.alt = '图片加载失败';
            console.error(`无法加载图片: ${imagePath}`);
        };
        
        imageJobName.textContent = `作业名称: ${image.job_name}`;
        imageJobLocation.textContent = `作业地点: ${image.job_location}`;
        cameraNamElement.textContent = `摄像头: ${image.camera_name}`;
        imageTimestamp.textContent = `时间: ${formatTimestamp(image.timestamp)}`;

        // 更新危险信息显示
        const hasDanger = image.has_danger || image.is_black_screen;
        document.getElementById('image-has-danger').textContent = `是否有危险: ${hasDanger ? '是' : '否'}`;

        let dangerTypes = '';
        if (image.is_black_screen) {
            dangerTypes = '黑屏';
        } else if (image.danger_types && image.danger_types.length > 0) {
            dangerTypes = image.danger_types.join(', ');
        } else {
            dangerTypes = '无';
        }
        document.getElementById('image-danger-types').textContent = `危险类型: ${dangerTypes}`;
        
        // 更新图片列表的选中状态
        const items = imageList.querySelectorAll('.image-list-item');
        items.forEach((item, i) => {
            if (i === index) {
                item.classList.add('selected');
                item.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            } else {
                item.classList.remove('selected');
            }
        });
    }
    
    /**
     * 显示上一张图片
     */
    function showPreviousImage() {
        if (abnormalImages.length === 0) return;
        
        let newIndex = currentImageIndex - 1;
        if (newIndex < 0) {
            newIndex = abnormalImages.length - 1;
        }
        showImage(newIndex);
    }
    
    /**
     * 显示下一张图片
     */
    function showNextImage() {
        if (abnormalImages.length === 0) return;
        
        let newIndex = currentImageIndex + 1;
        if (newIndex >= abnormalImages.length) {
            newIndex = 0;
        }
        showImage(newIndex);
    }
    
    /**
     * 清空图片显示
     */
    function clearImageDisplay() {
        currentImage.src = '';
        imageJobName.textContent = '作业名称: ';
        imageJobLocation.textContent = '作业地点: ';
        cameraNamElement.textContent = '摄像头: ';
        imageTimestamp.textContent = '时间: ';
        imageReason.textContent = '异常原因: ';
        currentImageIndex = -1;
    }
    
    /**
     * 停止监控
     */
    function stopMonitoring() {
        if (confirm('确定要停止监控吗？')) {
            showLoading();
            fetch('/stop_monitoring', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                alert('监控已停止');
                hideLoading();
            })
            .catch(error => {
                console.error('停止监控失败:', error);
                alert('停止监控失败');
                hideLoading();
            });
        }
    }
    
    /**
     * 刷新数据
     */
    function refreshData() {
        loadJobInfo();
        loadAbnormalImages();
    }
    
    /**
     * 显示加载中状态
     */
    function showLoading() {
        loadingModal.style.display = 'flex';
    }
    
    /**
     * 隐藏加载中状态
     */
    function hideLoading() {
        loadingModal.style.display = 'none';
    }
    
    /**
     * 格式化时间戳
     */
    function formatTimestamp(timestamp) {
        if (!timestamp) return '';

        // 支持两种格式：
        // 1. "YYYY-MM-DD HH:MM:SS" (新格式)
        // 2. "YYYYMMDD_HHMMSS" (旧格式)

        if (timestamp.includes('-') && timestamp.includes(':')) {
            // 新格式，直接返回
            return timestamp;
        } else {
            // 旧格式，需要转换
            const year = timestamp.slice(0, 4);
            const month = timestamp.slice(4, 6);
            const day = timestamp.slice(6, 8);
            const hour = timestamp.slice(9, 11);
            const minute = timestamp.slice(11, 13);
            const second = timestamp.slice(13, 15);

            return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
        }
    }
    
    /**
     * 为日期选择器格式化日期
     */
    function formatDateForInput(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    /**
     * 打开灯箱
     */
    function openLightbox() {
        if (currentImageIndex >= 0 && abnormalImages.length > 0) {
            updateLightboxContent(currentImageIndex);
            lightbox.style.display = 'flex'; // 使用flex显示以实现居中
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        }
    }

    /**
     * 关闭灯箱
     */
    function closeLightbox() {
        lightbox.style.display = 'none';
        document.body.style.overflow = 'auto'; // 恢复背景滚动
    }

    /**
     * 显示灯箱中的上一张图片
     */
    function showLightboxPrevious() {
        if (currentImageIndex > 0) {
            currentImageIndex--;
            updateLightboxContent(currentImageIndex);
            updateImageDisplay(); // 同步更新主界面
        }
    }

    /**
     * 显示灯箱中的下一张图片
     */
    function showLightboxNext() {
        if (currentImageIndex < abnormalImages.length - 1) {
            currentImageIndex++;
            updateLightboxContent(currentImageIndex);
            updateImageDisplay(); // 同步更新主界面
        }
    }

    /**
     * 更新灯箱内容
     */
    function updateLightboxContent(index) {
        if (index < 0 || index >= abnormalImages.length) return;

        const image = abnormalImages[index];

        // 组装图片完整路径（与主界面保持一致）
        let imagePath;
        if (image.image_path) {
            // 新格式: 使用完整路径
            imagePath = `/${image.image_path}`;
        } else if (image.dir) {
            // 兼容旧格式: 使用目录
            imagePath = `/screenshots/${image.dir}/${image.filepath}`;
        } else {
            // 兼容旧格式: 直接使用文件名
            imagePath = `/screenshots/${image.filepath}`;
        }

        // 更新图片
        lightboxImage.src = imagePath;
        lightboxImage.alt = `${image.job_name} - ${image.camera_name}`;
        lightboxImage.onerror = function() {
            // 如果图片加载失败，显示错误消息
            this.onerror = null;
            this.src = '/static/img/image_error.png';
            this.alt = '图片加载失败';
            console.error(`灯箱无法加载图片: ${imagePath}`);
        };

        // 更新信息
        lightboxJobName.textContent = image.job_name || '未知作业';
        lightboxJobLocation.textContent = image.job_location || '未知地点';
        lightboxCameraName.textContent = image.camera_name || '未知摄像头';
        lightboxTimestamp.textContent = image.timestamp || '未知时间';
        lightboxJobCategory.textContent = image.job_category || '未知类别';

        // 更新危险状态
        if (image.is_black_screen) {
            lightboxDangerStatus.textContent = '黑屏异常';
            lightboxDangerStatus.style.color = '#dc3545';
        } else if (image.has_danger) {
            lightboxDangerStatus.textContent = '检测到危险';
            lightboxDangerStatus.style.color = '#dc3545';
        } else {
            lightboxDangerStatus.textContent = '正常';
            lightboxDangerStatus.style.color = '#28a745';
        }

        // 更新危险类型
        if (image.danger_types && image.danger_types.length > 0) {
            lightboxDangerTypes.textContent = image.danger_types.join(', ');
            lightboxDangerTypes.style.color = '#dc3545';
        } else {
            lightboxDangerTypes.textContent = '无';
            lightboxDangerTypes.style.color = '#6c757d';
        }

        // 更新导航按钮状态
        lightboxPrev.disabled = (index === 0);
        lightboxNext.disabled = (index === abnormalImages.length - 1);

        // 重置缩放状态
        resetImageTransform();
    }

    /**
     * 重置图片变换状态
     */
    function resetImageTransform() {
        currentScale = 1;
        translateX = 0;
        translateY = 0;
        updateImageTransform();
        lightboxImage.style.cursor = 'zoom-in';
    }

    /**
     * 更新图片变换
     */
    function updateImageTransform() {
        // 应用边界限制
        const bounds = calculateImageBounds();
        translateX = Math.max(bounds.minX, Math.min(bounds.maxX, translateX));
        translateY = Math.max(bounds.minY, Math.min(bounds.maxY, translateY));

        lightboxImage.style.transform = `scale(${currentScale}) translate(${translateX}px, ${translateY}px)`;
    }

    /**
     * 计算图片移动边界
     */
    function calculateImageBounds() {
        const container = document.querySelector('.lightbox-image-container');
        const containerRect = container.getBoundingClientRect();
        const imageRect = lightboxImage.getBoundingClientRect();

        // 获取图片的原始尺寸（未缩放）
        const imageNaturalWidth = lightboxImage.naturalWidth || lightboxImage.width;
        const imageNaturalHeight = lightboxImage.naturalHeight || lightboxImage.height;

        // 计算图片在容器中的显示尺寸（考虑max-width和max-height限制）
        const containerWidth = containerRect.width - 40; // 减去padding
        const containerHeight = containerRect.height - 40;

        // 计算图片的实际显示尺寸（未缩放时）
        const imageAspectRatio = imageNaturalWidth / imageNaturalHeight;
        const containerAspectRatio = containerWidth / containerHeight;

        let displayWidth, displayHeight;
        if (imageAspectRatio > containerAspectRatio) {
            // 图片更宽，以宽度为准
            displayWidth = Math.min(containerWidth, imageNaturalWidth);
            displayHeight = displayWidth / imageAspectRatio;
        } else {
            // 图片更高，以高度为准
            displayHeight = Math.min(containerHeight, imageNaturalHeight);
            displayWidth = displayHeight * imageAspectRatio;
        }

        // 计算缩放后的尺寸
        const scaledWidth = displayWidth * currentScale;
        const scaledHeight = displayHeight * currentScale;

        // 计算允许的移动范围
        const maxMoveX = Math.max(0, (scaledWidth - containerWidth) / 2);
        const maxMoveY = Math.max(0, (scaledHeight - containerHeight) / 2);

        return {
            minX: -maxMoveX,
            maxX: maxMoveX,
            minY: -maxMoveY,
            maxY: maxMoveY
        };
    }

    /**
     * 处理图片缩放
     */
    function handleImageZoom(e) {
        e.preventDefault();

        const container = document.querySelector('.lightbox-image-container');
        const containerRect = container.getBoundingClientRect();
        const imageRect = lightboxImage.getBoundingClientRect();

        // 计算鼠标在容器中的相对位置
        const mouseX = e.clientX - containerRect.left;
        const mouseY = e.clientY - containerRect.top;

        // 计算鼠标在图片中的相对位置（考虑当前的transform）
        const imageX = (mouseX - containerRect.width / 2 - translateX) / currentScale;
        const imageY = (mouseY - containerRect.height / 2 - translateY) / currentScale;

        const delta = e.deltaY > 0 ? -0.1 : 0.1;
        const newScale = Math.max(0.5, Math.min(5, currentScale + delta));

        if (newScale !== currentScale) {
            // 计算新的translate值，保持鼠标位置不变
            translateX = mouseX - containerRect.width / 2 - imageX * newScale;
            translateY = mouseY - containerRect.height / 2 - imageY * newScale;

            currentScale = newScale;
            updateImageTransform();

            // 更新鼠标样式
            lightboxImage.style.cursor = currentScale > 1 ? 'zoom-out' : 'zoom-in';
        }
    }

    /**
     * 处理图片点击缩放
     */
    function handleImageClick(e) {
        e.stopPropagation(); // 防止触发灯箱关闭

        const container = document.querySelector('.lightbox-image-container');
        const containerRect = container.getBoundingClientRect();

        // 计算点击位置在容器中的相对位置
        const clickX = e.clientX - containerRect.left;
        const clickY = e.clientY - containerRect.top;

        if (currentScale === 1) {
            // 放大到2倍，以点击位置为中心
            const newScale = 2;

            // 计算点击位置在图片中的相对位置
            const imageX = (clickX - containerRect.width / 2) / currentScale;
            const imageY = (clickY - containerRect.height / 2) / currentScale;

            // 计算新的translate值
            translateX = clickX - containerRect.width / 2 - imageX * newScale;
            translateY = clickY - containerRect.height / 2 - imageY * newScale;

            currentScale = newScale;
            lightboxImage.style.cursor = 'zoom-out';
        } else {
            // 重置到原始大小
            resetImageTransform();
        }

        updateImageTransform();
    }

    /**
     * 处理鼠标按下（开始拖拽）
     */
    function handleMouseDown(e) {
        if (currentScale > 1) {
            isDragging = true;
            startX = e.clientX - translateX;
            startY = e.clientY - translateY;
            lightboxImage.style.cursor = 'grabbing';
            e.preventDefault();
        }
    }

    /**
     * 处理鼠标移动（拖拽）
     */
    function handleMouseMove(e) {
        if (isDragging && currentScale > 1) {
            const newTranslateX = e.clientX - startX;
            const newTranslateY = e.clientY - startY;

            // 应用边界限制
            const bounds = calculateImageBounds();
            translateX = Math.max(bounds.minX, Math.min(bounds.maxX, newTranslateX));
            translateY = Math.max(bounds.minY, Math.min(bounds.maxY, newTranslateY));

            // 直接应用transform，不调用updateImageTransform避免重复计算
            lightboxImage.style.transform = `scale(${currentScale}) translate(${translateX}px, ${translateY}px)`;
        }
    }

    /**
     * 处理鼠标释放（结束拖拽）
     */
    function handleMouseUp(e) {
        if (isDragging) {
            isDragging = false;
            lightboxImage.style.cursor = currentScale > 1 ? 'zoom-out' : 'zoom-in';
        }
    }
});
# 监控项目模块包
"""
监控项目模块化结构

modules/
├── __init__.py              # 模块包初始化
├── config.py               # 配置管理
├── browser_manager.py      # 浏览器管理
├── camera_monitor.py       # 摄像头监控
├── image_analyzer.py       # 图像分析
├── file_utils.py          # 文件处理工具
└── web_app.py             # Web应用
"""

__version__ = "1.0.0"
__author__ = "监控项目团队"

# 导入主要模块
from .config import Config, global_state, lock
from .browser_manager import BrowserManager
from .job_processor import JobProcessor
from .camera_monitor import CameraMonitor
from .image_analyzer import ImageAnalyzer
from .multi_model_analyzer import MultiModelAnalyzer
from .file_utils import FileUtils
from .web_app import create_app, run_app

__all__ = [
    'Config',
    'global_state',
    'BrowserManager',
    'JobProcessor',
    'CameraMonitor',
    'ImageAnalyzer',
    'FileUtils',
    'create_app',
    'run_app'
]

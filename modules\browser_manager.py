"""
浏览器管理模块
提供浏览器初始化、登录、页面操作等功能
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.edge.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from typing import Optional, List, Dict, Any

from .config import Config, global_state


class BrowserManager:
    """浏览器管理类"""
    
    def __init__(self):
        self.driver: Optional[webdriver.Edge] = None
        self.wait: Optional[WebDriverWait] = None
        self.main_window: Optional[str] = None
    
    def initialize_driver(self) -> bool:
        """
        初始化浏览器驱动
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            driver_path = Config.get_driver_path()
            service = Service(driver_path)
            
            # 配置浏览器选项
            options = webdriver.EdgeOptions()
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 创建驱动实例
            self.driver = webdriver.Edge(service=service, options=options)
            self.wait = WebDriverWait(self.driver, 20)
            
            # 设置全局驱动
            global_state.global_driver = self.driver

            # 最大化浏览器窗口
            self.driver.maximize_window()
            print("浏览器窗口已最大化")

            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            print("浏览器驱动初始化成功")
            return True
            
        except Exception as e:
            print(f"初始化浏览器驱动失败: {str(e)}")
            return False
    
    def login(self, username: str, password: str) -> bool:
        """
        执行登录流程

        Args:
            username: 用户名
            password: 密码

        Returns:
            bool: 登录是否成功
        """
        try:
            print("开始登录流程...")

            # 访问登录页
            self.driver.get(Config.LOGIN_URL)

            # 处理可能出现的任何警告
            try:
                alert = self.driver.switch_to.alert
                print(f"检测到警告: {alert.text}")
                alert.accept()  # 点击确定按钮
                time.sleep(1)
            except Exception as e:
                # 没有警告弹窗，继续正常流程
                pass

            # 输入凭据
            self._enter_credentials(username, password)

            # 处理登录后可能出现的警告
            try:
                alert = WebDriverWait(self.driver, 3).until(EC.alert_is_present())
                print(f"登录后检测到警告: {alert.text}")
                alert.accept()
            except Exception:
                pass

            # 等待公司Logo出现（标志登录成功或页面加载完成）
            try:
                self.wait.until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="app"]/div[1]/div[2]/div[1]/img'))
                )
                print("登录成功")
            except Exception as e:
                print(f"等待Logo出现时出错: {str(e)}")
                # 检查是否有警告
                try:
                    alert = self.driver.switch_to.alert
                    print(f"发现警告: {alert.text}")
                    alert.accept()
                except:
                    pass

            # 保存主窗口句柄
            self.main_window = self.driver.current_window_handle

            return True

        except Exception as e:
            print(f"登录过程中出错: {str(e)}")
            return False
    
    def _enter_credentials(self, username: str, password: str) -> None:
        """输入登录信息"""
        try:
            username_field = self.wait.until(
                EC.visibility_of_element_located((By.CSS_SELECTOR, "#username"))
            )
            username_field.clear()
            username_field.send_keys(username)

            password_field = self.wait.until(
                EC.visibility_of_element_located((By.CSS_SELECTOR, "#password"))
            )
            password_field.clear()
            password_field.send_keys(password)

            # 点击登录按钮
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="kc-submit"]'))
            ).click()

        except Exception as e:
            # 检查是否有意外警告弹出
            try:
                alert = self.wait._driver.switch_to.alert
                print(f"处理意外警告: {alert.text}")
                alert.accept()  # 点击确定
                # 刷新页面重试
                self.wait._driver.refresh()
                time.sleep(2)
                # 递归调用自身重试
                self._enter_credentials(username, password)
                return
            except Exception:
                # 如果不是警告问题，重新抛出原始异常
                raise e

        # 判断是否存在特定元素并点击
        try:
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="info_flash"]/div/div[3]/a[2]')),
            ).click()
        except (TimeoutException, NoSuchElementException):
            # 若元素不存在或超时未出现，跳过该步骤
            pass
    

    
    def navigate_to_target_page(self) -> bool:
        """导航到目标页面"""
        try:
            self.driver.get(Config.TARGET_URL)
            time.sleep(3)
            
            # 等待页面加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            return True
            
        except Exception as e:
            print(f"导航到目标页面失败: {str(e)}")
            return False
    
    def create_job_tab(self, job_url: str) -> Optional[str]:
        """
        为作业创建新标签页
        
        Args:
            job_url: 作业页面URL
            
        Returns:
            Optional[str]: 新标签页的句柄，失败时返回None
        """
        try:
            # 切换到主窗口
            self.driver.switch_to.window(self.main_window)
            time.sleep(1)
            
            # 创建新标签页
            self.driver.execute_script("window.open('about:blank');")
            time.sleep(1)
            
            # 获取新标签页句柄
            job_tab = self.driver.window_handles[-1]
            
            # 切换到新标签页
            self.driver.switch_to.window(job_tab)
            time.sleep(1)
            
            # 导航到作业页面
            self.driver.get(job_url)
            time.sleep(3)
            
            # 等待页面加载
            self.wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="app"]')))
            time.sleep(1)
            
            return job_tab
            
        except Exception as e:
            print(f"创建作业标签页失败: {str(e)}")
            return None
    
    def switch_to_tab(self, tab_handle: str) -> bool:
        """
        切换到指定标签页
        
        Args:
            tab_handle: 标签页句柄
            
        Returns:
            bool: 切换是否成功
        """
        try:
            self.driver.switch_to.window(tab_handle)
            return True
        except Exception as e:
            print(f"切换标签页失败: {str(e)}")
            return False
    
    def scroll_to_top(self) -> bool:
        """滚动到页面顶部"""
        try:
            # 多种滚动复位方式
            reset_methods = [
                "window.scrollTo(0, 0);",
                "document.documentElement.scrollTop = 0; document.body.scrollTop = 0;",
                "window.scrollTo({top: 0, left: 0, behavior: 'instant'});"
            ]
            
            for method in reset_methods:
                self.driver.execute_script(method)
                time.sleep(0.5)
                
                # 验证复位结果
                scroll_x = self.driver.execute_script(
                    'return window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;')
                scroll_y = self.driver.execute_script(
                    'return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;')
                
                if scroll_x == 0 and scroll_y == 0:
                    return True
            
            return False
            
        except Exception as e:
            print(f"滚动复位失败: {str(e)}")
            return False
    
    def get_scroll_position(self) -> tuple:
        """获取当前滚动位置"""
        try:
            scroll_x = self.driver.execute_script(
                'return window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;')
            scroll_y = self.driver.execute_script(
                'return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;')
            return (scroll_x, scroll_y)
        except:
            return (0, 0)
    
    def close(self):
        """关闭浏览器"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                global_state.global_driver = None
                print("浏览器已关闭")
        except Exception as e:
            print(f"关闭浏览器时出错: {str(e)}")
    
    def take_screenshot(self, filepath: str) -> bool:
        """
        截取整页截图

        Args:
            filepath: 保存路径

        Returns:
            bool: 截图是否成功
        """
        try:
            self.driver.save_screenshot(filepath)
            return True
        except Exception as e:
            print(f"截图失败: {str(e)}")
            return False

    def __del__(self):
        """析构函数，确保浏览器被关闭"""
        self.close()
